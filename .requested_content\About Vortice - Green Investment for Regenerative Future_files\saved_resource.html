<!DOCTYPE html>
<!-- saved from url=(0248)https://edit.framer.com/?framerSiteId=e44d407ad2e7cd1eab0eb2feac2f6d670b5dc47c66fcd6d94d3ec53bd31ca8f6&source=vorticegroup.org&features=%7B%22editorBarDisableFrameAncestorsSecurity%22%3Afalse%2C%22editorBarOnPageEditing%22%3Afalse%7D&forceShow=true -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <link rel="preload" as="script" href="./bootstrap.4458791b3dc75bc4246ead8f0e75235f4f43c930.js.download">
    <script>
Object.defineProperty(window, 'castlePublishableAPIKey', { value: Object.freeze("pk_xYqGbB61EcFiSh4LUshpT4DGoEQMKQMB") });
</script>
<script>Object.defineProperty(window, 'framerRelease', { value: Object.freeze({"channel":"stable"}) });;</script>
<script>Object.defineProperty(window, 'framerUser', { value: Object.freeze(null) });</script>
<script src="./bootstrap.4458791b3dc75bc4246ead8f0e75235f4f43c930.js.download"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="robots" content="noindex">
    
    <script type="text/javascript">
        Object.defineProperty(window, 'cssBundleURL', { value: "https://app.framerstatic.com/editorbar.JKPIZ2TG.css" });
        
        Object.defineProperty(window, 'deferredJsFiles', {
            value:["/s/app.4458791b3dc75bc4246ead8f0e75235f4f43c930/scripts/react/react.production.min.js","/s/app.4458791b3dc75bc4246ead8f0e75235f4f43c930/scripts/react-dom/react-dom.production.min.js","/s/app.4458791b3dc75bc4246ead8f0e75235f4f43c930/scripts/react-dom/react-dom-server-legacy.browser.production.min.js"]
        });
    </script>
    
        
        <link href="https://app.framerstatic.com/chunk-E5XQBAUL.mjs" rel="modulepreload">
<link href="https://app.framerstatic.com/chunk-DFNFZLDR.mjs" rel="modulepreload">
<link href="https://app.framerstatic.com/chunk-MCY6KDOW.mjs" rel="modulepreload">
<link href="https://app.framerstatic.com/chunk-AHQIRSXG.mjs" rel="modulepreload">
    
<link rel="stylesheet" href="./editorbar.JKPIZ2TG.css"></head>

<body class="notranslate editorbar framer-theme-dark framer-release-stable">
<div id="root" role="presentation"><div class="stack_s1n8l1x5 row_rr3s3mz center_c1arh7l2 nowrap_n61wj3j flex-start_f1nit8mr wrapper_w1wtsf9k" role="presentation" style="--shrink: 0; gap: 10px;"><div class="stack_s1n8l1x5 row_rr3s3mz center_c1arh7l2 nowrap_n61wj3j center_c1e6tv3r button_blajnuz" role="button" aria-label="Open Framer Menu" style="--shrink: 0; gap: 10px;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 15" width="10" height="16" class="logo_lbr5chv" aria-hidden="true"><path d="M 0 0 L 10 0 L 10 5 L 5 5 Z M 0 5 L 5 5 L 10 10 L 0 10 Z M 5 10 L 5 15 L 0 10 Z" fill="currentColor"></path></svg></div></div></div>


<!--


-->


    <script>
        // Keep these keys in sync with DarkModeProvider.tsx
        var userDarkMode = localStorage.getItem("dark-mode") === "true"
        var overrideSystem = localStorage.getItem("dark-mode-override-system") === "true"
        var systemDarkMode = matchMedia("(prefers-color-scheme: dark)").matches
        var isDarkMode = overrideSystem ? userDarkMode : systemDarkMode
        if (isDarkMode) {
            document.body.classList.add("framer-theme-dark")
        }

        // Set release channel as class on body.
        var framerReleaseChannel = window.framerRelease && window.framerRelease.channel
        if (framerReleaseChannel) {
            document.body.classList.add("framer-release-" + framerReleaseChannel)
        }
    </script>




<script src="./editorbar.AYDXJKKJ.mjs" type="module"></script>


<script src="./react.production.min.js.download" defer=""></script><script src="./react-dom.production.min.js.download" defer=""></script><script src="./react-dom-server-legacy.browser.production.min.js.download" defer=""></script><div id="drag-overlay" style="position: absolute; z-index: 1000000; inset: 0px; display: none;"></div></body></html>