import{D as O,E as L,Ea as J,F,G as a,H as x,I as N,K as S,L as C,M as V,N as A,O as M,P as G,Q as v,S as H,U,X,e as d,h as k,i as P,j as B,ka as j,l as D,oa as W,p as T,q as z,sa as Z}from"./chunk-B2VYWNJS.mjs";import"./chunk-HZL4YIMB.mjs";import{b as R,c as e}from"./chunk-A3IIQ6X3.mjs";var oe="default"in F?O:F,f={},K=oe;f.createRoot=K.createRoot;f.hydrateRoot=K.hydrateRoot;var Q=f.createRoot,Y=f.hydrateRoot;var u={augiA20Il:{elements:{YI_K7oNN_:"why"},page:a(()=>import("./VlWC6qMy3RZYWY_S8ca1Dt2bFw0K94v3JfsTkZ5EPis.74SFBJQV.mjs")),path:"/"},X2U2b9uTz:{elements:{AuCXI7xoh:"our-mission",AVueOdN0h:"mobile-joinus",bFXovGNPs:"pillar",eUNDZd5l5:"what-we-do",H_djDszHM:"team",I9XK22TcX:"contact",y3gts4HDb:"joinus"},page:a(()=>import("./j75fx276vz1oq8XXZltAQDVFjY3oj3yCeGcifHH1OYk.M3SFAJKP.mjs")),path:"/about-vortice"},Zv0vbTBQD:{elements:{},page:a(()=>import("./_07UR1DYfTJsyuBGfinncY9pt93pxDyGPEV2nsgrVe8.WLZ6AHLX.mjs")),path:"/ghg"},AfrE6osV7:{elements:{},page:a(()=>import("./EYEPHwyhi2xBihQnyyW9ld7ZWyao1xAP7aOGTO7GAwU.E2BSYC4J.mjs")),path:"/MNTP"},qXlmalrkf:{elements:{},page:a(()=>import("./NzECX3pbImn_fgM-Rxw_-xACdRAXShNd8wfr3WGjUl4.F5IW6J7M.mjs")),path:"/green-companies-perform-better"},kcIPYlx7B:{elements:{},page:a(()=>import("./7Tko2MXPTj3MXT_CvwI9oLjZYg4jTofCUhOl607DkdQ.Y227Z5TZ.mjs")),path:"/earth-overshootday"},GwmIy4ETx:{elements:{},page:a(()=>import("./bM6VY6APg8_CUsgTLFaIvTLIUE2SucgM6L1RuKPV4Tg.XTSFKVWC.mjs")),path:"/earth-basketball"},LeJZ_8rua:{elements:{},page:a(()=>import("./F2_5nujUxfWWL5FnQq0vBgSIrq453beXyExBTmiFmuM.CVLESEDS.mjs")),path:"/why-regenerative"},PdGFgGxig:{page:a(()=>import("./PdGFgGxig-LJGBF5UV.mjs"))}},_=[{code:"en",id:"default",name:"English",slug:""}],b={},$="e44d407ad2e7cd1eab0eb2feac2f6d670b5dc47c66fcd6d94d3ec53bd31ca8f6";async function se({routeId:n,pathVariables:l,localeId:m}){let t=u[n].page.preload(),s=d(W,{isWebsite:!0,routeId:n,pathVariables:l,routes:u,collectionUtils:b,framerSiteId:$,notFoundPage:a(()=>import("./<EMAIL>")),isReducedMotion:!1,localeId:m,locales:_,preserveQueryParams:void 0,siteCanonicalURL:"https://vorticegroup.org",EditorBar:typeof e<"u"?(()=>{if(!/bot|-google|google-|yandex|ia_archiver|crawl|spider/iu.test(R.userAgent))return a(async()=>{let r={__version:1,framer:{useCurrentRoute:N,useLocaleInfo:V,useRouter:x},react:{createElement:d,memo:k,useCallback:B,useEffect:D,useRef:T,useState:z},"react-dom":{createPortal:L}};e.__framer_editorBarDependencies=r;let{createEditorBar:g}=await import("https://edit.framer.com/init.mjs");return{default:g({dependencies:r})}})})():void 0}),i=d(X,{children:s,value:{editorBarDisableFrameAncestorsSecurity:!1,editorBarOnPageEditing:!1,motionDivToDiv:!1,motionDivToDivBackgroundImage:!1,pauseOffscreen:!0,replaceNestedLinks:!0,yieldOnTap:!1}}),o=d(j,{children:i}),p=d(C,{children:o,value:{routes:{}}});return await t,p}var ee=typeof document<"u";if(ee){e.__framer_importFromPackage=(l,m)=>()=>d(H,{error:'Package component not supported: "'+m+'" in "'+l+'"'}),e.process={...e.process,env:{...e.process?e.process.env:void 0,NODE_ENV:"production"}},e.__framer_events=e.__framer_events||[],U();let n=document.getElementById("main");"framerHydrateV2"in n.dataset?q(!0,n):q(!1,n)}function ie(){ee&&e.__framer_events.push(arguments)}async function q(n,l){function m(t,s,i=!0){if(t.caught||e.__framer_hadFatalError)return;let o=s?.componentStack;if(i){if(console.warn(`Recoverable error has happened. Please check any custom code or code overrides to fix server/client mismatches:
`,t,o),Math.random()>.01)return}else console.error(`Fatal crash has happened. If you are the author of this website, please report this issue to the Framer team via https://www.framer.community/:
`,t,o);ie(i?"published_site_load_recoverable_error":"published_site_load_error",{message:String(t),componentStack:o,stack:o?void 0:t instanceof Error&&typeof t.stack=="string"?t.stack:null})}try{let t,s,i,o;if(n){let r=JSON.parse(l.dataset.framerHydrateV2);t=r.routeId,s=r.localeId,i=r.pathVariables,o=r.breakpoints,t=v(u,t)}else{v(u,void 0);let r=G(u,decodeURIComponent(location.pathname),!0,_);t=r.routeId,s=r.localeId,i=r.pathVariables}let p=se({routeId:t,localeId:s,pathVariables:i});typeof e<"u"&&(async()=>{let r=u[t],g="default",y=_.find(({id:c})=>s?c===s:c===g).code,I=null;if(r?.collectionId&&b){let c=await b[r.collectionId]?.(),[w]=Object.values(i);c&&typeof w=="string"&&(I=await c.getRecordIdBySlug(w,y||void 0)??null)}let E=Intl.DateTimeFormat().resolvedOptions(),te=E.timeZone,re=E.locale;await new Promise(c=>{document.prerendering?document.addEventListener("prerenderingchange",c,{once:!0}):c()}),e.__framer_events.push(["published_site_pageview",{framerSiteId:$??null,routePath:r?.path||"/",collectionItemId:I,framerLocale:y||null,webPageId:r?.abTestingVariantId??t,abTestId:r?.abTestId,referrer:document.referrer||null,url:e.location.href,hostname:e.location.hostname||null,pathname:e.location.pathname||null,hash:e.location.hash||null,search:e.location.search||null,timezone:te,locale:re},"eager"]),await S({priority:"background",ensureContinueBeforeUnload:!0,continueAfter:"paint"}),document.dispatchEvent(new CustomEvent("framer:pageview",{detail:{framerLocale:y||null}}))})();let h=await p;n?(J("framer-rewrite-breakpoints",()=>{Z(o),e.__framer_onRewriteBreakpoints?.(o)}),P(()=>{M(),A(),Y(l,h,{onRecoverableError:m})})):Q(l,{onRecoverableError:m}).render(h)}catch(t){throw m(t,void 0,!1),t}}export{se as getPageRoot};
//# sourceMappingURL=script_main.3NJL6AHG.mjs.map
