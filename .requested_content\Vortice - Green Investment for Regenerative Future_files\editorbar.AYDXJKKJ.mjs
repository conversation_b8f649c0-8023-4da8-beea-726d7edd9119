import{a as s}from"https://app.framerstatic.com/chunk-E5XQBAUL.mjs";import{a}from"https://app.framerstatic.com/chunk-MCY6KDOW.mjs";import"https://app.framerstatic.com/chunk-DFNFZLDR.mjs";import"https://app.framerstatic.com/chunk-AHQIRSXG.mjs";async function d(){let e=window.deferredJsFiles;if(!e)return;let o=e.map(r=>new Promise((i,n)=>{let t=document.createElement("script");t.src=r,t.async=!1,t.defer=!0,t.onload=()=>i(),t.onerror=()=>n(),document.body.appendChild(t)}));await Promise.all(o)}async function f(){let e=document.createElement("link");e.setAttribute("rel","stylesheet"),e.setAttribute("href",window.cssBundleURL);let o=new Promise((r,i)=>{e.onload=()=>r(),e.onerror=()=>i()});document.head.appendChild(e),await o}async function l(){let e=new URLSearchParams(window.location.search),o=e.get("framerSiteId"),r=e.get("source");if(!o||!r)return null;let i=e.get("features"),n={};try{i&&(n=JSON.parse(i))}catch{}Object.defineProperty(window,"editorBarFeatures",{value:Object.freeze(n),writable:!1});let t=await s(o);if(!e.has("forceShow")&&a(r)&&t.status!=="success")return;await Promise.all([d(),f()]);let{renderEditorBar:c}=await import("https://app.framerstatic.com/render-Z3FDFM6E.mjs");await c(t,o)}l().catch(console.error);
//# sourceMappingURL=https://app.framerstatic.com/editorbar.AYDXJKKJ.mjs.map
