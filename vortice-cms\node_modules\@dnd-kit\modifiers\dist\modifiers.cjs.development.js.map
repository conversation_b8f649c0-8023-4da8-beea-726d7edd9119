{"version": 3, "file": "modifiers.cjs.development.js", "sources": ["../src/createSnapModifier.ts", "../src/restrictToHorizontalAxis.ts", "../src/utilities/restrictToBoundingRect.ts", "../src/restrictToParentElement.ts", "../src/restrictToFirstScrollableAncestor.ts", "../src/restrictToVerticalAxis.ts", "../src/restrictToWindowEdges.ts", "../src/snapCenterToCursor.ts"], "sourcesContent": ["import type {Modifier} from '@dnd-kit/core';\n\nexport function createSnapModifier(gridSize: number): Modifier {\n  return ({transform}) => ({\n    ...transform,\n    x: Math.ceil(transform.x / gridSize) * gridSize,\n    y: Math.ceil(transform.y / gridSize) * gridSize,\n  });\n}\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToHorizontalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    y: 0,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {Transform} from '@dnd-kit/utilities';\n\nexport function restrictToBoundingRect(\n  transform: Transform,\n  rect: ClientRect,\n  boundingRect: ClientRect\n): Transform {\n  const value = {\n    ...transform,\n  };\n\n  if (rect.top + transform.y <= boundingRect.top) {\n    value.y = boundingRect.top - rect.top;\n  } else if (\n    rect.bottom + transform.y >=\n    boundingRect.top + boundingRect.height\n  ) {\n    value.y = boundingRect.top + boundingRect.height - rect.bottom;\n  }\n\n  if (rect.left + transform.x <= boundingRect.left) {\n    value.x = boundingRect.left - rect.left;\n  } else if (\n    rect.right + transform.x >=\n    boundingRect.left + boundingRect.width\n  ) {\n    value.x = boundingRect.left + boundingRect.width - rect.right;\n  }\n\n  return value;\n}\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToParentElement: Modifier = ({\n  containerNodeRect,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (!draggingNodeRect || !containerNodeRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, containerNodeRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToFirstScrollableAncestor: Modifier = ({\n  draggingNodeRect,\n  transform,\n  scrollableAncestorRects,\n}) => {\n  const firstScrollableAncestorRect = scrollableAncestorRects[0];\n\n  if (!draggingNodeRect || !firstScrollableAncestorRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(\n    transform,\n    draggingNodeRect,\n    firstScrollableAncestorRect\n  );\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToVerticalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    x: 0,\n  };\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToWindowEdges: Modifier = ({\n  transform,\n  draggingNodeRect,\n  windowRect,\n}) => {\n  if (!draggingNodeRect || !windowRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, windowRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {getEventCoordinates} from '@dnd-kit/utilities';\n\nexport const snapCenterToCursor: Modifier = ({\n  activatorEvent,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (draggingNodeRect && activatorEvent) {\n    const activatorCoordinates = getEventCoordinates(activatorEvent);\n\n    if (!activatorCoordinates) {\n      return transform;\n    }\n\n    const offsetX = activatorCoordinates.x - draggingNodeRect.left;\n    const offsetY = activatorCoordinates.y - draggingNodeRect.top;\n\n    return {\n      ...transform,\n      x: transform.x + offsetX - draggingNodeRect.width / 2,\n      y: transform.y + offsetY - draggingNodeRect.height / 2,\n    };\n  }\n\n  return transform;\n};\n"], "names": ["createSnapModifier", "gridSize", "transform", "x", "Math", "ceil", "y", "restrictToHorizontalAxis", "restrictToBoundingRect", "rect", "boundingRect", "value", "top", "bottom", "height", "left", "right", "width", "restrictToParentElement", "containerNodeRect", "draggingNodeRect", "restrictToFirstScrollableAncestor", "scrollableAncestorRects", "firstScrollableAncestorRect", "restrictToVerticalAxis", "restrictToWindowEdges", "windowRect", "snapCenterToCursor", "activatorEvent", "activatorCoordinates", "getEventCoordinates", "offsetX", "offsetY"], "mappings": ";;;;;;SAEgBA,mBAAmBC;EACjC,OAAO;IAAA,IAAC;MAACC;KAAF;IAAA,OAAkB,EACvB,GAAGA,SADoB;MAEvBC,CAAC,EAAEC,IAAI,CAACC,IAAL,CAAUH,SAAS,CAACC,CAAV,GAAcF,QAAxB,IAAoCA,QAFhB;MAGvBK,CAAC,EAAEF,IAAI,CAACC,IAAL,CAAUH,SAAS,CAACI,CAAV,GAAcL,QAAxB,IAAoCA;KAHlC;GAAP;AAKD;;MCNYM,wBAAwB,GAAa;MAAC;IAACL;;EAClD,OAAO,EACL,GAAGA,SADE;IAELI,CAAC,EAAE;GAFL;AAID,CALM;;SCCSE,uBACdN,WACAO,MACAC;EAEA,MAAMC,KAAK,GAAG,EACZ,GAAGT;GADL;;EAIA,IAAIO,IAAI,CAACG,GAAL,GAAWV,SAAS,CAACI,CAArB,IAA0BI,YAAY,CAACE,GAA3C,EAAgD;IAC9CD,KAAK,CAACL,CAAN,GAAUI,YAAY,CAACE,GAAb,GAAmBH,IAAI,CAACG,GAAlC;GADF,MAEO,IACLH,IAAI,CAACI,MAAL,GAAcX,SAAS,CAACI,CAAxB,IACAI,YAAY,CAACE,GAAb,GAAmBF,YAAY,CAACI,MAF3B,EAGL;IACAH,KAAK,CAACL,CAAN,GAAUI,YAAY,CAACE,GAAb,GAAmBF,YAAY,CAACI,MAAhC,GAAyCL,IAAI,CAACI,MAAxD;;;EAGF,IAAIJ,IAAI,CAACM,IAAL,GAAYb,SAAS,CAACC,CAAtB,IAA2BO,YAAY,CAACK,IAA5C,EAAkD;IAChDJ,KAAK,CAACR,CAAN,GAAUO,YAAY,CAACK,IAAb,GAAoBN,IAAI,CAACM,IAAnC;GADF,MAEO,IACLN,IAAI,CAACO,KAAL,GAAad,SAAS,CAACC,CAAvB,IACAO,YAAY,CAACK,IAAb,GAAoBL,YAAY,CAACO,KAF5B,EAGL;IACAN,KAAK,CAACR,CAAN,GAAUO,YAAY,CAACK,IAAb,GAAoBL,YAAY,CAACO,KAAjC,GAAyCR,IAAI,CAACO,KAAxD;;;EAGF,OAAOL,KAAP;AACD;;MC5BYO,uBAAuB,GAAa;MAAC;IAChDC,iBADgD;IAEhDC,gBAFgD;IAGhDlB;;;EAEA,IAAI,CAACkB,gBAAD,IAAqB,CAACD,iBAA1B,EAA6C;IAC3C,OAAOjB,SAAP;;;EAGF,OAAOM,sBAAsB,CAACN,SAAD,EAAYkB,gBAAZ,EAA8BD,iBAA9B,CAA7B;AACD,CAVM;;MCAME,iCAAiC,GAAa;MAAC;IAC1DD,gBAD0D;IAE1DlB,SAF0D;IAG1DoB;;EAEA,MAAMC,2BAA2B,GAAGD,uBAAuB,CAAC,CAAD,CAA3D;;EAEA,IAAI,CAACF,gBAAD,IAAqB,CAACG,2BAA1B,EAAuD;IACrD,OAAOrB,SAAP;;;EAGF,OAAOM,sBAAsB,CAC3BN,SAD2B,EAE3BkB,gBAF2B,EAG3BG,2BAH2B,CAA7B;AAKD,CAhBM;;MCDMC,sBAAsB,GAAa;MAAC;IAACtB;;EAChD,OAAO,EACL,GAAGA,SADE;IAELC,CAAC,EAAE;GAFL;AAID,CALM;;MCEMsB,qBAAqB,GAAa;MAAC;IAC9CvB,SAD8C;IAE9CkB,gBAF8C;IAG9CM;;;EAEA,IAAI,CAACN,gBAAD,IAAqB,CAACM,UAA1B,EAAsC;IACpC,OAAOxB,SAAP;;;EAGF,OAAOM,sBAAsB,CAACN,SAAD,EAAYkB,gBAAZ,EAA8BM,UAA9B,CAA7B;AACD,CAVM;;MCDMC,kBAAkB,GAAa;MAAC;IAC3CC,cAD2C;IAE3CR,gBAF2C;IAG3ClB;;;EAEA,IAAIkB,gBAAgB,IAAIQ,cAAxB,EAAwC;IACtC,MAAMC,oBAAoB,GAAGC,6BAAmB,CAACF,cAAD,CAAhD;;IAEA,IAAI,CAACC,oBAAL,EAA2B;MACzB,OAAO3B,SAAP;;;IAGF,MAAM6B,OAAO,GAAGF,oBAAoB,CAAC1B,CAArB,GAAyBiB,gBAAgB,CAACL,IAA1D;IACA,MAAMiB,OAAO,GAAGH,oBAAoB,CAACvB,CAArB,GAAyBc,gBAAgB,CAACR,GAA1D;IAEA,OAAO,EACL,GAAGV,SADE;MAELC,CAAC,EAAED,SAAS,CAACC,CAAV,GAAc4B,OAAd,GAAwBX,gBAAgB,CAACH,KAAjB,GAAyB,CAF/C;MAGLX,CAAC,EAAEJ,SAAS,CAACI,CAAV,GAAc0B,OAAd,GAAwBZ,gBAAgB,CAACN,MAAjB,GAA0B;KAHvD;;;EAOF,OAAOZ,SAAP;AACD,CAvBM;;;;;;;;;;"}