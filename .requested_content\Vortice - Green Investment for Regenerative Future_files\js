
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"google.com.hk"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":13,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":4},{"function":"__ccd_ga_first","priority":12,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":18},{"function":"__set_product_settings","priority":11,"vtp_instanceDestinationId":"G-15FN2QGQ72","vtp_foreignTldMacroResult":["macro",1],"vtp_isChinaVipRegionMacroResult":["macro",2],"tag_id":17},{"function":"__ccd_ga_regscope","priority":10,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":16},{"function":"__ccd_em_download","priority":9,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":15},{"function":"__ccd_em_form","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":14},{"function":"__ccd_em_outbound_click","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":13},{"function":"__ccd_em_page_view","priority":6,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":12},{"function":"__ccd_em_scroll","priority":5,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":11},{"function":"__ccd_em_site_search","priority":4,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":10},{"function":"__ccd_em_video","priority":3,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":9},{"function":"__ccd_conversion_marking","priority":2,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":8},{"function":"__ccd_auto_redact","priority":1,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":7},{"function":"__gct","vtp_trackingId":"G-15FN2QGQ72","vtp_sessionDuration":0,"tag_id":1},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-15FN2QGQ72","tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",13]],[["if",1],["add",0,14,12,11,10,9,8,7,6,5,4,3,2,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_auto_redact",[46,"a"],[50,"u",[46,"aI"],[36,[2,[15,"aI"],"replace",[7,[15,"t"],"\\$1"]]]],[50,"v",[46,"aI"],[52,"aJ",[30,["c",[15,"aI"]],[15,"aI"]]],[52,"aK",[7]],[65,"aL",[2,[15,"aJ"],"split",[7,""]],[46,[53,[52,"aM",[7,["u",[15,"aL"]]]],[52,"aN",["d",[15,"aL"]]],[22,[12,[15,"aN"],[45]],[46,[53,[36,["d",["u",[15,"aI"]]]]]]],[22,[21,[15,"aN"],[15,"aL"]],[46,[53,[2,[15,"aM"],"push",[7,[15,"aN"]]],[22,[21,[15,"aL"],[2,[15,"aL"],"toLowerCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toLowerCase",[7]]]]]]],[46,[22,[21,[15,"aL"],[2,[15,"aL"],"toUpperCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toUpperCase",[7]]]]]]]]]]]]],[22,[18,[17,[15,"aM"],"length"],1],[46,[53,[2,[15,"aK"],"push",[7,[0,[0,"(?:",[2,[15,"aM"],"join",[7,"|"]]],")"]]]]],[46,[53,[2,[15,"aK"],"push",[7,[16,[15,"aM"],0]]]]]]]]],[36,[2,[15,"aK"],"join",[7,""]]]],[50,"w",[46,"aI","aJ","aK"],[52,"aL",["y",[15,"aI"],[15,"aK"]]],[22,[28,[15,"aL"]],[46,[36,[15,"aI"]]]],[22,[28,[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[41,"aM"],[3,"aM",[17,[15,"aL"],"search"]],[65,"aN",[15,"aJ"],[46,[53,[52,"aO",[7,["u",[15,"aN"]],["v",[15,"aN"]]]],[65,"aP",[15,"aO"],[46,[53,[52,"aQ",[30,[16,[15,"s"],[15,"aP"]],[43,[15,"s"],[15,"aP"],["b",[0,[0,"([?&]",[15,"aP"]],"=)([^&]*)"],"gi"]]]],[3,"aM",[2,[15,"aM"],"replace",[7,[15,"aQ"],[0,"$1",[15,"q"]]]]]]]]]]],[22,[20,[15,"aM"],[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[22,[20,[16,[15,"aM"],0],"&"],[46,[3,"aM",[2,[15,"aM"],"substring",[7,1]]]]],[22,[21,[16,[15,"aM"],0],"?"],[46,[3,"aM",[0,"?",[15,"aM"]]]]],[22,[20,[15,"aM"],"?"],[46,[3,"aM",""]]],[43,[15,"aL"],"search",[15,"aM"]],[36,["z",[15,"aL"],[15,"aK"]]]],[50,"y",[46,"aI","aJ"],[22,[20,[15,"aJ"],[17,[15,"r"],"PATH"]],[46,[53,[3,"aI",[0,[15,"x"],[15,"aI"]]]]]],[36,["f",[15,"aI"]]]],[50,"z",[46,"aI","aJ"],[41,"aK"],[3,"aK",""],[22,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[46,[53,[41,"aL"],[3,"aL",""],[22,[30,[17,[15,"aI"],"username"],[17,[15,"aI"],"password"]],[46,[53,[3,"aL",[0,[15,"aL"],[0,[0,[0,[17,[15,"aI"],"username"],[39,[17,[15,"aI"],"password"],":",""]],[17,[15,"aI"],"password"]],"@"]]]]]],[3,"aK",[0,[0,[0,[17,[15,"aI"],"protocol"],"//"],[15,"aL"]],[17,[15,"aI"],"host"]]]]]],[36,[0,[0,[0,[15,"aK"],[17,[15,"aI"],"pathname"]],[17,[15,"aI"],"search"]],[17,[15,"aI"],"hash"]]]],[50,"aA",[46,"aI","aJ"],[41,"aK"],[3,"aK",[2,[15,"aI"],"replace",[7,[15,"m"],[15,"q"]]]],[22,[30,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[20,[15,"aJ"],[17,[15,"r"],"PATH"]]],[46,[53,[52,"aL",["y",[15,"aK"],[15,"aJ"]]],[22,[20,[15,"aL"],[44]],[46,[36,[15,"aK"]]]],[52,"aM",[17,[15,"aL"],"search"]],[52,"aN",[2,[15,"aM"],"replace",[7,[15,"n"],[15,"q"]]]],[22,[20,[15,"aM"],[15,"aN"]],[46,[36,[15,"aK"]]]],[43,[15,"aL"],"search",[15,"aN"]],[3,"aK",["z",[15,"aL"],[15,"aJ"]]]]]],[36,[15,"aK"]]],[50,"aB",[46,"aI"],[22,[20,[15,"aI"],[15,"p"]],[46,[53,[36,[17,[15,"r"],"PATH"]]]],[46,[22,[21,[2,[15,"o"],"indexOf",[7,[15,"aI"]]],[27,1]],[46,[53,[36,[17,[15,"r"],"URL"]]]],[46,[53,[36,[17,[15,"r"],"TEXT"]]]]]]]],[50,"aC",[46,"aI","aJ"],[41,"aK"],[3,"aK",false],[52,"aL",["e",[15,"aI"]]],[38,[15,"aL"],[46,"string","array","object"],[46,[5,[46,[52,"aM",["aA",[15,"aI"],[15,"aJ"]]],[22,[21,[15,"aI"],[15,"aM"]],[46,[53,[36,[15,"aM"]]]]],[4]]],[5,[46,[53,[41,"aN"],[3,"aN",0],[63,[7,"aN"],[23,[15,"aN"],[17,[15,"aI"],"length"]],[33,[15,"aN"],[3,"aN",[0,[15,"aN"],1]]],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]]],[4]]],[5,[46,[54,"aN",[15,"aI"],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]],[4]]]]],[36,[39,[15,"aK"],[15,"aI"],[44]]]],[50,"aH",[46,"aI","aJ"],[52,"aK",[30,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"I"]]],[7]]],[22,[20,[2,[15,"aK"],"indexOf",[7,[15,"aJ"]]],[27,1]],[46,[53,[2,[15,"aK"],"push",[7,[15,"aJ"]]]]]],[2,[15,"aI"],"setMetadata",[7,[17,[15,"h"],"I"],[15,"aK"]]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","encodeUriComponent"]],[52,"e",["require","getType"]],[52,"f",["require","parseUrl"]],[52,"g",["require","internal.registerCcdCallback"]],[52,"h",[15,"__module_metadataSchema"]],[52,"i",[17,[15,"a"],"instanceDestinationId"]],[52,"j",[17,[15,"a"],"redactEmail"]],[52,"k",[17,[15,"a"],"redactQueryParams"]],[52,"l",[39,[15,"k"],[2,[15,"k"],"split",[7,","]],[7]]],[22,[1,[28,[17,[15,"l"],"length"]],[28,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["b","[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}","gi"]],[52,"n",["b",[0,"([A-Z0-9._-]|%25|%2B)+%40[A-Z0-9.-]","+\\.[A-Z]{2,}"],"gi"]],[52,"o",[7,"page_location","page_referrer","page_path","link_url","video_url","form_destination"]],[52,"p","page_path"],[52,"q","(redacted)"],[52,"r",[8,"TEXT",0,"URL",1,"PATH",2]],[52,"s",[8]],[52,"t",["b","([\\\\^$.|?*+(){}]|\\[|\\[)","g"]],[52,"x","http://."],[52,"aD",15],[52,"aE",16],[52,"aF",23],[52,"aG",24],["g",[15,"i"],[51,"",[7,"aI"],[22,[15,"j"],[46,[53,[52,"aJ",[2,[15,"aI"],"getHitKeys",[7]]],[65,"aK",[15,"aJ"],[46,[53,[22,[20,[15,"aK"],"_sst_parameters"],[46,[6]]],[52,"aL",[2,[15,"aI"],"getHitData",[7,[15,"aK"]]]],[22,[28,[15,"aL"]],[46,[6]]],[52,"aM",["aB",[15,"aK"]]],[52,"aN",["aC",[15,"aL"],[15,"aM"]]],[22,[21,[15,"aN"],[44]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aK"],[15,"aN"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"Y"]]],[15,"aF"],[15,"aD"]]]]]]]]]]]],[22,[17,[15,"l"],"length"],[46,[53,[65,"aJ",[15,"o"],[46,[53,[52,"aK",[2,[15,"aI"],"getHitData",[7,[15,"aJ"]]]],[22,[28,[15,"aK"]],[46,[6]]],[52,"aL",[39,[20,[15,"aJ"],[15,"p"]],[17,[15,"r"],"PATH"],[17,[15,"r"],"URL"]]],[52,"aM",["w",[15,"aK"],[15,"l"],[15,"aL"]]],[22,[21,[15,"aM"],[15,"aK"]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aJ"],[15,"aM"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"Y"]]],[15,"aG"],[15,"aE"]]]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"M"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"O"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"P"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"V"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"W"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"q",[46,"w"],[36,[1,[15,"w"],[21,[2,[2,[15,"w"],"toLowerCase",[7]],"match",[7,[15,"p"]]],[45]]]]],[50,"r",[46,"w"],[52,"x",[2,[17,[15,"w"],"pathname"],"split",[7,"."]]],[52,"y",[39,[18,[17,[15,"x"],"length"],1],[16,[15,"x"],[37,[17,[15,"x"],"length"],1]],""]],[36,[16,[2,[15,"y"],"split",[7,"/"]],0]]],[50,"s",[46,"w"],[36,[39,[12,[2,[17,[15,"w"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"w"],"pathname"],[0,"/",[17,[15,"w"],"pathname"]]]]],[50,"t",[46,"w"],[41,"x"],[3,"x",""],[22,[1,[15,"w"],[17,[15,"w"],"href"]],[46,[53,[41,"y"],[3,"y",[2,[17,[15,"w"],"href"],"indexOf",[7,"#"]]],[3,"x",[39,[23,[15,"y"],0],[17,[15,"w"],"href"],[2,[17,[15,"w"],"href"],"substring",[7,0,[15,"y"]]]]]]]],[36,[15,"x"]]],[50,"v",[46,"w"],[52,"x",[8]],[43,[15,"x"],[15,"i"],true],[43,[15,"x"],[15,"e"],true],[43,[15,"w"],"eventMetadata",[15,"x"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmDownloadActivity"]],[52,"e","speculative"],[52,"f","ae_block_downloads"],[52,"g","file_download"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","parseUrl"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"u",["l",[8,"checkValidation",true]]],[22,[28,[15,"u"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"w","x"],["x"],[52,"y",[8,"eventId",[16,[15,"w"],"gtm.uniqueEventId"],"deferrable",true]],[52,"z",[16,[15,"w"],"gtm.elementUrl"]],[52,"aA",["n",[15,"z"]]],[22,[28,[15,"aA"]],[46,[36]]],[52,"aB",["r",[15,"aA"]]],[22,[28,["q",[15,"aB"]]],[46,[53,[36]]]],[52,"aC",[8,"link_id",[16,[15,"w"],"gtm.elementId"],"link_url",["t",[15,"aA"]],"link_text",[16,[15,"w"],"gtm.elementText"],"file_name",["s",[15,"aA"]],"file_extension",[15,"aB"]]],["v",[15,"y"]],["o",["m"],[15,"g"],[15,"aC"],[15,"y"]]],[15,"u"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"t",[46,"aA"],[52,"aB",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aB"],"event_usage",[7,8]],[43,[15,"aA"],[15,"m"],[15,"aB"]]],[50,"u",[46,"aA","aB"],[52,"aC",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aC"],[15,"l"],true],[43,[15,"aC"],[15,"g"],true],[22,[16,[15,"aB"],"gtm.formCanceled"],[46,[53,[43,[15,"aC"],[15,"n"],true]]]],[43,[15,"aA"],[15,"m"],[15,"aC"]]],[50,"v",[46,"aA","aB","aC"],[52,"aD",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[20,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aD"],"length"],0],[46,[53,["s",[15,"aD"],[15,"aA"],[15,"aB"],[15,"aC"]]]]],[52,"aE",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[21,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aE"],"length"],0],[46,[53,[43,[15,"aC"],"deferrable",true],["s",[15,"aE"],[15,"aA"],[15,"aB"],[15,"aC"]]]]]],[52,"b",["require","internal.isFeatureEnabled"]],[52,"c",[15,"__module_featureFlags"]],[52,"d",["require","internal.getProductSettingsParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmFormActivity"]],[52,"g","speculative"],[52,"h","ae_block_form"],[52,"i","form_submit"],[52,"j","form_start"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m","eventMetadata"],[52,"n","form_event_canceled"],[52,"o",[17,[15,"a"],"instanceDestinationId"]],[22,["d",[15,"o"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"A",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"e"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"k"],true]],[52,"p",["require","internal.addFormInteractionListener"]],[52,"q",["require","internal.addFormSubmitListener"]],[52,"r",["require","internal.getDestinationIds"]],[52,"s",["require","internal.sendGtagEvent"]],[52,"w",[8]],[52,"x",[51,"",[7,"aA","aB"],[22,[15,"aB"],[46,["aB"]]],[52,"aC",[16,[15,"aA"],"gtm.elementId"]],[22,[16,[15,"w"],[15,"aC"]],[46,[36]]],[43,[15,"w"],[15,"aC"],true],[52,"aD",[8,"form_id",[15,"aC"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"aA"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"aA"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"aA"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"aA"],"gtm.interactedFormFieldPosition"]]],[52,"aE",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aE"]],["u",[15,"aE"],[15,"aA"]],["v",[15,"j"],[15,"aD"],[15,"aE"]]]],[52,"y",["b",[17,[15,"c"],"DS"]]],[52,"z",[51,"",[7,"aA","aB"],["x",[15,"aA"],[44]],[52,"aC",[8,"form_id",[16,[15,"aA"],"gtm.elementId"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"y"],[16,[15,"aA"],"gtm.formSubmitElementText"],[16,[15,"aA"],"gtm.formSubmitButtonText"]]]],[43,[15,"aC"],"event_callback",[15,"aB"]],[52,"aD",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aD"]],["u",[15,"aD"],[15,"aA"]],["v",[15,"i"],[15,"aC"],[15,"aD"]]]],[22,[15,"y"],[46,[53,[52,"aA",["require","internal.addDataLayerEventListener"]],[52,"aB",["require","internal.enableAutoEventOnFormSubmit"]],[52,"aC",["require","internal.enableAutoEventOnFormInteraction"]],[52,"aD",["aC"]],[22,[28,[15,"aD"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formInteract",[15,"x"],[15,"aD"]],[52,"aE",["aB",[8,"checkValidation",false,"waitForTags",false]]],[22,[28,[15,"aE"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formSubmit",[15,"z"],[15,"aE"]]]],[46,[53,["p",[15,"x"]],["q",[15,"z"],[8,"waitForCallbacks",false,"checkValidation",false]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"r",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"s",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",[17,[15,"x"],"hostname"]],[52,"z",[2,[15,"y"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"z"],[16,[15,"z"],0]],[46,[3,"y",[2,[15,"y"],"substring",[7,[17,[16,[15,"z"],0],"length"]]]]]],[36,[15,"y"]]],[50,"t",[46,"x"],[22,[28,[15,"x"]],[46,[36,false]]],[52,"y",[2,[17,[15,"x"],"hostname"],"toLowerCase",[7]]],[22,[28,[15,"y"]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[2,["s",["p",["o"]]],"toLowerCase",[7]]],[41,"aA"],[3,"aA",[37,[17,[15,"y"],"length"],[17,[15,"z"],"length"]]],[22,[1,[18,[15,"aA"],0],[29,[2,[15,"z"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aA"],[3,"aA",[37,[15,"aA"],1]]],[3,"z",[0,".",[15,"z"]]]]]],[22,[1,[19,[15,"aA"],0],[12,[2,[15,"y"],"indexOf",[7,[15,"z"],[15,"aA"]]],[15,"aA"]]],[46,[53,[36,false]]]],[36,true]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"i"],true],[43,[15,"y"],[15,"e"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmOutboundClickActivity"]],[52,"e","speculative"],[52,"f","ae_block_outbound_click"],[52,"g","click"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.getRemoteConfigParameter"]],[52,"o",["require","getUrl"]],[52,"p",["require","parseUrl"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"u",["n",[15,"j"],"cross_domain_conditions"]],[52,"v",["l",[8,"affiliateDomains",[15,"u"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"x","y"],[52,"z",["p",[16,[15,"x"],"gtm.elementUrl"]]],[22,[28,["t",[15,"z"]]],[46,[53,["y"],[36]]]],[52,"aA",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_classes",[16,[15,"x"],"gtm.elementClasses"],"link_url",["r",[15,"z"]],"link_domain",["s",[15,"z"]],"outbound",true]],[43,[15,"aA"],"event_callback",[15,"y"]],[52,"aB",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"],"deferrable",true]],["w",[15,"aB"]],["q",["m"],[15,"g"],[15,"aA"],[15,"aB"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[17,[15,"f"],"G"],true],[43,[15,"s"],[17,[15,"f"],"AG"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[8,"interval",1000,"useV2EventName",true]],[52,"p",["l",[15,"o"]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"r","s"],["s"],[52,"t",[16,[15,"r"],"gtm.oldUrl"]],[22,[20,[16,[15,"r"],"gtm.newUrl"],[15,"t"]],[46,[36]]],[52,"u",[16,[15,"r"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"u"],"pushState"],[21,[15,"u"],"popstate"]],[21,[15,"u"],"replaceState"]],[46,[53,[36]]]],[52,"v",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"v"],"page_location",[16,[15,"r"],"gtm.newUrl"]],[43,[15,"v"],"page_referrer",[15,"t"]]]]],[52,"w",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[21,[17,[15,"a"],"deferPageView"],false],[46,[53,[43,[15,"w"],"deferrable",true]]]],["q",[15,"w"]],["n",["m"],[15,"h"],[15,"v"],[15,"w"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"p",[46,"q"],[52,"r",[8]],[43,[15,"r"],[15,"i"],true],[43,[15,"r"],[15,"e"],true],[43,[15,"q"],"eventMetadata",[15,"r"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmScrollActivity"]],[52,"e","speculative"],[52,"f","ae_block_scroll"],[52,"g","scroll"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnScroll"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",["l",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"o"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.scrollDepth",[51,"",[7,"q","r"],["r"],[52,"s",[8,"eventId",[16,[15,"q"],"gtm.uniqueEventId"],"deferrable",true]],[52,"t",[8,"percent_scrolled",[16,[15,"q"],"gtm.scrollThreshold"]]],["p",[15,"s"]],["n",["m"],[15,"g"],[15,"t"],[15,"s"]]],[15,"o"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"A",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"B",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[15,"k"],true],[43,[15,"t"],[15,"e"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmVideoActivity"]],[52,"e","speculative"],[52,"f","ae_block_video"],[52,"g","video_start"],[52,"h","video_progress"],[52,"i","video_complete"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"l"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"l"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["require","internal.addDataLayerEventListener"]],[52,"n",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"o",["require","internal.getDestinationIds"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",["n",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"j"],true]],["m","gtm.video",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.videoStatus"]],[41,"v"],[22,[20,[15,"u"],"start"],[46,[53,[3,"v",[15,"g"]]]],[46,[22,[20,[15,"u"],"progress"],[46,[53,[3,"v",[15,"h"]]]],[46,[22,[20,[15,"u"],"complete"],[46,[53,[3,"v",[15,"i"]]]],[46,[53,[36]]]]]]]],[52,"w",[8,"video_current_time",[16,[15,"s"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"s"],"gtm.videoDuration"],"video_percent",[16,[15,"s"],"gtm.videoPercent"],"video_provider",[16,[15,"s"],"gtm.videoProvider"],"video_title",[16,[15,"s"],"gtm.videoTitle"],"video_url",[16,[15,"s"],"gtm.videoUrl"],"visible",[16,[15,"s"],"gtm.videoVisible"]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"],"deferrable",true]],["r",[15,"x"]],["p",["o"],[15,"v"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"EI"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"AB"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CT"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AC"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",0],[52,"c",1],[52,"d",2],[52,"e",3],[52,"f",4],[52,"g",5],[52,"h",6],[52,"i",7],[52,"j",8],[52,"k",9],[52,"l",10],[52,"m",13],[52,"n",16],[52,"o",17],[52,"p",19],[52,"q",20],[52,"r",21],[52,"s",22],[52,"t",23],[52,"u",24],[52,"v",25],[52,"w",26],[52,"x",27],[52,"y",28],[52,"z",29],[52,"aA",30],[52,"aB",31],[52,"aC",32],[52,"aD",33],[52,"aE",34],[52,"aF",35],[52,"aG",36],[52,"aH",37],[52,"aI",38],[52,"aJ",39],[52,"aK",40],[52,"aL",41],[52,"aM",47],[52,"aN",42],[52,"aO",43],[52,"aP",44],[52,"aQ",45],[52,"aR",46],[52,"aS",49],[52,"aT",52],[52,"aU",53],[52,"aV",54],[52,"aW",56],[52,"aX",59],[52,"aY",60],[52,"aZ",62],[52,"bA",63],[52,"bB",66],[52,"bC",68],[52,"bD",69],[52,"bE",71],[52,"bF",72],[52,"bG",75],[52,"bH",78],[52,"bI",83],[52,"bJ",84],[52,"bK",87],[52,"bL",88],[52,"bM",89],[52,"bN",90],[52,"bO",91],[52,"bP",92],[52,"bQ",93],[52,"bR",94],[52,"bS",95],[52,"bT",97],[52,"bU",100],[52,"bV",101],[52,"bW",102],[52,"bX",103],[52,"bY",104],[52,"bZ",106],[52,"cA",107],[52,"cB",108],[52,"cC",109],[52,"cD",111],[52,"cE",112],[52,"cF",113],[52,"cG",114],[52,"cH",115],[52,"cI",116],[52,"cJ",117],[52,"cK",118],[52,"cL",119],[52,"cM",120],[52,"cN",121],[52,"cO",122],[52,"cP",123],[52,"cQ",125],[52,"cR",126],[52,"cS",127],[52,"cT",128],[52,"cU",129],[52,"cV",130],[52,"cW",131],[52,"cX",132],[52,"cY",133],[52,"cZ",134],[52,"dA",135],[52,"dB",136],[52,"dC",137],[52,"dD",138],[52,"dE",139],[52,"dF",140],[52,"dG",141],[52,"dH",142],[52,"dI",143],[52,"dJ",144],[52,"dK",145],[52,"dL",146],[52,"dM",147],[52,"dN",148],[52,"dO",149],[52,"dP",152],[52,"dQ",153],[52,"dR",154],[52,"dS",155],[52,"dT",156],[52,"dU",157],[52,"dV",158],[52,"dW",159],[52,"dX",160],[52,"dY",162],[52,"dZ",164],[52,"eA",165],[52,"eB",168],[52,"eC",169],[52,"eD",170],[52,"eE",171],[52,"eF",174],[52,"eG",175],[52,"eH",176],[52,"eI",177],[52,"eJ",178],[52,"eK",183],[52,"eL",185],[52,"eM",186],[52,"eN",187],[52,"eO",188],[52,"eP",189],[52,"eQ",190],[52,"eR",191],[52,"eS",192],[52,"eT",193],[52,"eU",195],[52,"eV",196],[52,"eW",197],[52,"eX",198],[52,"eY",199],[52,"eZ",200],[52,"fA",201],[52,"fB",202],[52,"fC",203],[52,"fD",204],[52,"fE",205],[52,"fF",206],[52,"fG",207],[52,"fH",208],[52,"fI",209],[52,"fJ",210],[52,"fK",211],[36,[8,"E",[15,"f"],"F",[15,"g"],"EM",[15,"eN"],"EO",[15,"eP"],"FA",[15,"fB"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"EA",[15,"eB"],"O",[15,"p"],"EW",[15,"eX"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DX",[15,"dY"],"AJ",[15,"aK"],"AK",[15,"aL"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AL",[15,"aM"],"FC",[15,"fD"],"ER",[15,"eS"],"AR",[15,"aS"],"EN",[15,"eO"],"AT",[15,"aU"],"AU",[15,"aV"],"AS",[15,"aT"],"AV",[15,"aW"],"AW",[15,"aX"],"EC",[15,"eD"],"FJ",[15,"fK"],"EE",[15,"eF"],"EP",[15,"eQ"],"AX",[15,"aY"],"DY",[15,"dZ"],"FG",[15,"fH"],"EI",[15,"eJ"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"EQ",[15,"eR"],"BD",[15,"bE"],"BE",[15,"bF"],"FE",[15,"fF"],"BF",[15,"bG"],"FF",[15,"fG"],"BG",[15,"bH"],"EG",[15,"eH"],"BH",[15,"bI"],"EF",[15,"eG"],"BI",[15,"bJ"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"DZ",[15,"eA"],"BS",[15,"bT"],"BT",[15,"bU"],"BU",[15,"bV"],"BJ",[15,"bK"],"FD",[15,"fE"],"EX",[15,"eY"],"BV",[15,"bW"],"BW",[15,"bX"],"EV",[15,"eW"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"FB",[15,"fC"],"CE",[15,"cF"],"CF",[15,"cG"],"CG",[15,"cH"],"CI",[15,"cJ"],"CH",[15,"cI"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"EY",[15,"eZ"],"EH",[15,"eI"],"ES",[15,"eT"],"CO",[15,"cP"],"EK",[15,"eL"],"ED",[15,"eE"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"EL",[15,"eM"],"CS",[15,"cT"],"FI",[15,"fJ"],"CT",[15,"cU"],"CU",[15,"cV"],"FH",[15,"fI"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"EB",[15,"eC"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"],"DG",[15,"dH"],"DH",[15,"dI"],"EZ",[15,"fA"],"DI",[15,"dJ"],"DJ",[15,"dK"],"ET",[15,"eU"],"EU",[15,"eV"],"DK",[15,"dL"],"B",[15,"c"],"D",[15,"e"],"C",[15,"d"],"DL",[15,"dM"],"DM",[15,"dN"],"DN",[15,"dO"],"DO",[15,"dP"],"DP",[15,"dQ"],"A",[15,"b"],"DQ",[15,"dR"],"DR",[15,"dS"],"DS",[15,"dT"],"DT",[15,"dU"],"DU",[15,"dV"],"EJ",[15,"eK"],"DV",[15,"dW"],"DW",[15,"dX"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","gclgb"],[52,"t","gclid"],[52,"u","gclgs"],[52,"v","gcllp"],[52,"w","gclst"],[52,"x","ads_data_redaction"],[52,"y","allow_ad_personalization_signals"],[52,"z","allow_direct_google_requests"],[52,"aA","allow_google_signals"],[52,"aB","auid"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_id"],[52,"aK","conversion_linker"],[52,"aL","conversion_api"],[52,"aM","cookie_deprecation"],[52,"aN","cookie_expires"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","shipping"],[52,"aX","engagement_time_msec"],[52,"aY","estimated_delivery_date"],[52,"aZ","event_developer_id_string"],[52,"bA","event"],[52,"bB","event_timeout"],[52,"bC","first_party_collection"],[52,"bD","gdpr_applies"],[52,"bE","google_analysis_params"],[52,"bF","_google_ng"],[52,"bG","gpp_sid"],[52,"bH","gpp_string"],[52,"bI","gsa_experiment_id"],[52,"bJ","gtag_event_feature_usage"],[52,"bK","iframe_state"],[52,"bL","ignore_referrer"],[52,"bM","is_passthrough"],[52,"bN","_lps"],[52,"bO","language"],[52,"bP","merchant_feed_label"],[52,"bQ","merchant_feed_language"],[52,"bR","merchant_id"],[52,"bS","new_customer"],[52,"bT","page_hostname"],[52,"bU","page_path"],[52,"bV","page_referrer"],[52,"bW","page_title"],[52,"bX","_platinum_request_status"],[52,"bY","restricted_data_processing"],[52,"bZ","screen_resolution"],[52,"cA","search_term"],[52,"cB","send_page_view"],[52,"cC","server_container_url"],[52,"cD","session_duration"],[52,"cE","session_engaged_time"],[52,"cF","session_id"],[52,"cG","_shared_user_id"],[52,"cH","topmost_url"],[52,"cI","transaction_id"],[52,"cJ","transport_url"],[52,"cK","update"],[52,"cL","_user_agent_architecture"],[52,"cM","_user_agent_bitness"],[52,"cN","_user_agent_full_version_list"],[52,"cO","_user_agent_mobile"],[52,"cP","_user_agent_model"],[52,"cQ","_user_agent_platform"],[52,"cR","_user_agent_platform_version"],[52,"cS","_user_agent_wow64"],[52,"cT","user_data_auto_latency"],[52,"cU","user_data_auto_meta"],[52,"cV","user_data_auto_multi"],[52,"cW","user_data_auto_selectors"],[52,"cX","user_data_auto_status"],[52,"cY","user_data_mode"],[52,"cZ","user_id"],[52,"dA","user_properties"],[52,"dB","us_privacy_string"],[52,"dC","value"],[52,"dD","_fpm_parameters"],[52,"dE","_host_name"],[52,"dF","_in_page_command"],[52,"dG","non_personalized_ads"],[52,"dH","conversion_label"],[52,"dI","page_location"],[52,"dJ","global_developer_id_string"],[52,"dK","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DG",[15,"dH"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AS",[15,"aT"],"AT",[15,"aU"],"AU",[15,"aV"],"AV",[15,"aW"],"AW",[15,"aX"],"AX",[15,"aY"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"DI",[15,"dJ"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"BJ",[15,"bK"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"DH",[15,"dI"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"DJ",[15,"dK"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"B",[15,"c"],"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k","l"],[22,[20,[15,"k"],[44]],[46,[53,[3,"k",[20,[2,[15,"j"],"indexOf",[7,"AW-"]],0]]]]],["c",[15,"j"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[20,[15,"n"],[15,"g"]],[20,[15,"n"],[15,"f"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[22,[1,[28,[15,"k"]],[2,[15,"m"],"getMetadata",[7,[15,"h"]]]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_id",[44]]],[2,[15,"m"],"setHitData",[7,"form_name",[44]]],[2,[15,"m"],"setHitData",[7,"form_destination",[44]]],[2,[15,"m"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"n"],[15,"f"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"n"],[15,"g"]],[46,[53,[2,[15,"m"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_form"],[52,"f","form_submit"],[52,"g","form_start"],[52,"h","form_event_canceled"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"Y"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"BU"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"BU"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"AG"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"O"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"m",[46,"p","q","r"],[50,"w",[46,"y"],[52,"z",[16,[15,"l"],[15,"y"]]],[22,[28,[15,"z"]],[46,[36]]],[53,[41,"aA"],[3,"aA",0],[63,[7,"aA"],[23,[15,"aA"],[17,[15,"z"],"length"]],[33,[15,"aA"],[3,"aA",[0,[15,"aA"],1]]],[46,[53,[52,"aB",[16,[15,"z"],[15,"aA"]]],["t",[15,"s"],[17,[15,"aB"],"name"],[17,[15,"aB"],"value"]]]]]]],[50,"x",[46,"y"],[22,[30,[28,[15,"u"]],[21,[17,[15,"u"],"length"],2]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[16,[15,"y"],[15,"v"]]],[22,[20,[15,"z"],[44]],[46,[53,[3,"z",[16,[15,"y"],[15,"u"]]]]]],[36,[28,[28,[15,"z"]]]]],[22,[28,[15,"q"]],[46,[36]]],[52,"s",[30,[17,[15,"p"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"t",["h",[15,"f"],[15,"r"]]],[52,"u",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"r"]]],["$0"]]],[52,"v",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"r"]]],["$0"]]],[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[15,"q"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[16,[15,"q"],[15,"y"]]],[22,[30,[17,[15,"z"],"disallowAllRegions"],["x",[17,[15,"z"],"disallowedRegions"]]],[46,[53,["w",[17,[15,"z"],"redactFieldGroup"]]]]]]]]]],[50,"n",[46,"p"],[52,"q",[8]],[22,[28,[15,"p"]],[46,[36,[15,"q"]]]],[52,"r",[2,[15,"p"],"split",[7,","]]],[53,[41,"s"],[3,"s",0],[63,[7,"s"],[23,[15,"s"],[17,[15,"r"],"length"]],[33,[15,"s"],[3,"s",[0,[15,"s"],1]]],[46,[53,[52,"t",[2,[16,[15,"r"],[15,"s"]],"trim",[7]]],[22,[28,[15,"t"]],[46,[6]]],[52,"u",[2,[15,"t"],"split",[7,"-"]]],[52,"v",[16,[15,"u"],0]],[52,"w",[39,[20,[17,[15,"u"],"length"],2],[15,"t"],[44]]],[22,[30,[28,[15,"v"]],[21,[17,[15,"v"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"w"],[44]],[30,[23,[17,[15,"w"],"length"],4],[18,[17,[15,"w"],"length"],6]]],[46,[53,[6]]]],[43,[15,"q"],[15,"t"],true]]]]],[36,[15,"q"]]],[50,"o",[46,"p"],[22,[28,[17,[15,"p"],"settingsTable"]],[46,[36,[7]]]],[52,"q",[8]],[53,[41,"r"],[3,"r",0],[63,[7,"r"],[23,[15,"r"],[17,[17,[15,"p"],"settingsTable"],"length"]],[33,[15,"r"],[3,"r",[0,[15,"r"],1]]],[46,[53,[52,"s",[16,[17,[15,"p"],"settingsTable"],[15,"r"]]],[52,"t",[17,[15,"s"],"redactFieldGroup"]],[22,[28,[16,[15,"l"],[15,"t"]]],[46,[6]]],[43,[15,"q"],[15,"t"],[8,"redactFieldGroup",[15,"t"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"u",[16,[15,"q"],[15,"t"]]],[22,[17,[15,"s"],"disallowAllRegions"],[46,[53,[43,[15,"u"],"disallowAllRegions",true],[6]]]],[43,[15,"u"],"disallowedRegions",["n",[17,[15,"s"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"q"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[41,"i"],[41,"j"],[41,"k"],[52,"l",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"m"],"B",[15,"o"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_auto_redact":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_em_download":{"2":true,"5":true}
,
"__ccd_em_form":{"2":true,"5":true}
,
"__ccd_em_outbound_click":{"2":true,"5":true}
,
"__ccd_em_page_view":{"2":true,"5":true}
,
"__ccd_em_scroll":{"2":true,"5":true}
,
"__ccd_em_site_search":{"2":true,"5":true}
,
"__ccd_em_video":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__c":{}
,
"__ccd_auto_redact":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_auto_redact"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__set_product_settings"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=da(this),fa=function(a,b){if(b)a:{for(var c=ea,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
fa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ha=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;
if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ma;a:{var na={a:!0},oa={};try{oa.__proto__=na;ma=oa.a;break a}catch(a){}ma=!1}ja=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var pa=ja,ra=function(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(pa)pa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Cq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ta=function(a){return a instanceof Array?a:sa(l(a))},va=function(a){return ua(a,a)},ua=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},wa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};fa("Object.assign",function(a){return a||wa});
var xa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ya=this||self,za=function(a,b){function c(){}c.prototype=b.prototype;a.Cq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ar=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Aa=function(a,b){this.type=a;this.data=b};var Ba=function(){this.map=new Map;this.C=new Set};k=Ba.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.zl=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Ca=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Ba.prototype.wa=function(){return Ca(this,1)};Ba.prototype.ac=function(){return Ca(this,2)};Ba.prototype.Jb=function(){return Ca(this,3)};var Da=function(){this.map={};this.C={}};k=Da.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.zl=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ea=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Da.prototype.wa=function(){return Ea(this,1)};Da.prototype.ac=function(){return Ea(this,2)};Da.prototype.Jb=function(){return Ea(this,3)};var Fa=function(){};Fa.prototype.reset=function(){};var Ha=[],Ia={};function Ja(a){return Ha[a]===void 0?!1:Ha[a]};var Ka=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.tb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ja(16)?new Ba:new Da};k=Ka.prototype;k.add=function(a,b){this.tb||this.values.set(a,b)};k.sh=function(a,b){this.tb||this.values.zl(a,b)};k.set=function(a,b){this.tb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Ka(this.P,this);this.C&&a.Ob(this.C);a.Xc(this.H);a.Ld(this.N);return a};k.Dd=function(){return this.P};k.Ob=function(a){this.C=a};k.jm=function(){return this.C};k.Xc=function(a){this.H=a};k.kj=function(){return this.H};k.Ta=function(){this.tb=!0};k.Ld=function(a){this.N=a};k.sb=function(){return this.N};var La=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.tb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.sh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.tb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.tb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new La(this.ba,this);this.H&&a.Ob(this.H);a.Xc(this.N);a.Ld(this.P);return a};k.Dd=function(){return this.ba};k.Ob=function(a){this.H=a};k.jm=function(){return this.H};k.Xc=function(a){this.N=a};k.kj=function(){return this.N};k.Ta=function(){this.tb=!0};k.Ld=function(a){this.P=a};k.sb=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.xm=a;this.am=c===void 0?!1:c;this.debugInfo=[];this.C=b};ra(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=new Map;function Qa(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ra(a,e.value),c instanceof Aa);e=d.next());return c}function Ra(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f,g=String(d);Ja(18)?(f=Pa.get(g))||(f=a.get(g)):f=a.get(g);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ta(e)))}catch(m){var h=a.jm();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Ta=function(){this.H=new Fa;this.C=Ja(17)?new La(this.H):new Ka(this.H)};k=Ta.prototype;k.Dd=function(){return this.H};k.Ob=function(a){this.C.Ob(a)};k.Xc=function(a){this.C.Xc(a)};k.execute=function(a){return this.Lj([a].concat(ta(xa.apply(1,arguments))))};k.Lj=function(){for(var a,b=l(xa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ra(this.C,c.value);return a};
k.mo=function(a){var b=xa.apply(1,arguments),c=this.C.rb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ra(c,f.value);return d};k.Ta=function(){this.C.Ta()};var Ua=function(){this.Ca=!1;this.aa=new Da};k=Ua.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};k.Ta=function(){this.Ca=!0};k.tb=function(){return this.Ca};function Va(){for(var a=Wa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Za(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Wa,$a;function ab(a){Wa=Wa||Za();$a=$a||Va();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Wa[m],Wa[n],Wa[p],Wa[q])}return b.join("")}
function bb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=$a[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Wa=Wa||Za();$a=$a||Va();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var cb={};function db(a,b){cb[a]=cb[a]||[];cb[a][b]=!0}function eb(){cb.GTAG_EVENT_FEATURE_CHANNEL=fb}function gb(a){var b=cb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return ab(c.join("")).replace(/\.+$/,"")}function hb(){for(var a=[],b=cb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function ib(){}function jb(a){return typeof a==="function"}function lb(a){return typeof a==="string"}function mb(a){return typeof a==="number"&&!isNaN(a)}function nb(a){return Array.isArray(a)?a:[a]}function ob(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function pb(a,b){if(!mb(a)||!mb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function qb(a,b){for(var c=new rb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function sb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function tb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ub(a){return Math.round(Number(a))||0}function vb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function wb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function xb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function yb(){return new Date(Date.now())}function zb(){return yb().getTime()}var rb=function(){this.prefix="gtm.";this.values={}};rb.prototype.set=function(a,b){this.values[this.prefix+a]=b};rb.prototype.get=function(a){return this.values[this.prefix+a]};rb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ab(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Bb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Cb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Db(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Eb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Fb(a,b){var c=x;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function Gb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Hb=/^\w{1,9}$/;function Ib(a,b){a=a||{};b=b||",";var c=[];sb(a,function(d,e){Hb.test(d)&&e&&c.push(d)});return c.join(b)}function Jb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Kb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Lb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Mb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Nb=globalThis.trustedTypes,Ob;function Pb(){var a=null;if(!Nb)return a;try{var b=function(c){return c};a=Nb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Qb(){Ob===void 0&&(Ob=Pb());return Ob};var Rb=function(a){this.C=a};Rb.prototype.toString=function(){return this.C+""};function Tb(a){var b=a,c=Qb(),d=c?c.createScriptURL(b):b;return new Rb(d)}function Ub(a){if(a instanceof Rb)return a.C;throw Error("");};var Vb=va([""]),Wb=ua(["\x00"],["\\0"]),Xb=ua(["\n"],["\\n"]),Yb=ua(["\x00"],["\\u0000"]);function Zb(a){return a.toString().indexOf("`")===-1}Zb(function(a){return a(Vb)})||Zb(function(a){return a(Wb)})||Zb(function(a){return a(Xb)})||Zb(function(a){return a(Yb)});var $b=function(a){this.C=a};$b.prototype.toString=function(){return this.C};var ac=function(a){this.Vp=a};function bc(a){return new ac(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var cc=[bc("data"),bc("http"),bc("https"),bc("mailto"),bc("ftp"),new ac(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function dc(a){var b;b=b===void 0?cc:b;if(a instanceof $b)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof ac&&d.Vp(a))return new $b(a)}}var ec=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function fc(a){var b;if(a instanceof $b)if(a instanceof $b)b=a.C;else throw Error("");else b=ec.test(a)?a:void 0;return b};function hc(a,b){var c=fc(b);c!==void 0&&(a.action=c)};function ic(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var jc=function(a){this.C=a};jc.prototype.toString=function(){return this.C+""};var lc=function(){this.C=kc[0].toLowerCase()};lc.prototype.toString=function(){return this.C};function mc(a,b){var c=[new lc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof lc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var nc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function oc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,pc=window.history,z=document,qc=navigator;function rc(){var a;try{a=qc.serviceWorker}catch(b){return}return a}var sc=z.currentScript,tc=sc&&sc.src;function uc(a,b){var c=x[a];x[a]=c===void 0?b:c;return x[a]}function vc(a){return(qc.userAgent||"").indexOf(a)!==-1}function wc(){return vc("Firefox")||vc("FxiOS")}function xc(){return(vc("GSA")||vc("GoogleApp"))&&(vc("iPhone")||vc("iPad"))}function yc(){return vc("Edg/")||vc("EdgA/")||vc("EdgiOS/")}
var zc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Ac={onload:1,src:1,width:1,height:1,style:1};function Bc(a,b,c){b&&sb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Cc(a,b,c,d,e){var f=z.createElement("script");Bc(f,d,zc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Tb(oc(a));f.src=Ub(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Dc(){if(tc){var a=tc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Ec(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Bc(g,c,Ac);d&&sb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Fc(a,b,c,d){return Gc(a,b,c,d)}function Hc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Ic(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Jc(a){x.setTimeout(a,0)}function Kc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Lc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Mc(a){var b=z.createElement("div"),c=b,d,e=oc("A<div>"+a+"</div>"),f=Qb(),g=f?f.createHTML(e):e;d=new jc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof jc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Nc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Oc(a,b,c){var d;try{d=qc.sendBeacon&&qc.sendBeacon(a)}catch(e){db("TAGGING",15)}d?b==null||b():Gc(a,b,c)}function Pc(a,b){try{return qc.sendBeacon(a,b)}catch(c){db("TAGGING",15)}return!1}var Qc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Sc(a,b,c,d,e){if(Tc()){var f=Object.assign({},Qc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Hh)return e==null||e(),!1;if(b){var h=
Pc(a,b);h?d==null||d():e==null||e();return h}Uc(a,d,e);return!0}function Tc(){return typeof x.fetch==="function"}function Vc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Wc(){var a=x.performance;if(a&&jb(a.now))return a.now()}
function Xc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Yc(){return x.performance||void 0}function Zc(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Gc=function(a,b,c,d){var e=new Image(1,1);Bc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Uc=Oc;function $c(a,b){return this.evaluate(a)&&this.evaluate(b)}function ad(a,b){return this.evaluate(a)===this.evaluate(b)}function bd(a,b){return this.evaluate(a)||this.evaluate(b)}function cd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function dd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function ed(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ua&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var fd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,gd=function(a){if(a==null)return String(a);var b=fd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},hd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},id=function(a){if(!a||gd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!hd(a,"constructor")&&!hd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
hd(a,b)},jd=function(a,b){var c=b||(gd(a)=="array"?[]:{}),d;for(d in a)if(hd(a,d)){var e=a[d];gd(e)=="array"?(gd(c[d])!="array"&&(c[d]=[]),c[d]=jd(e,c[d])):id(e)?(id(c[d])||(c[d]={}),c[d]=jd(e,c[d])):c[d]=e}return c};function kd(a){if(a==void 0||Array.isArray(a)||id(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function ld(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var md=function(a){a=a===void 0?[]:a;this.aa=new Da;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(ld(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=md.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof md?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!ld(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else ld(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():ld(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Jb=function(){for(var a=this.aa.Jb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){ld(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ta(xa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=xa.apply(2,arguments);return b===void 0&&c.length===0?new md(this.values.splice(a)):new md(this.values.splice.apply(this.values,[a,b||0].concat(ta(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ta(xa.apply(0,arguments)))};k.has=function(a){return ld(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ta=function(){this.Ca=!0;Object.freeze(this.values)};k.tb=function(){return this.Ca};
function nd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var od=function(a,b){this.functionName=a;this.ze=b;this.aa=new Da;this.Ca=!1};k=od.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new md(this.wa())};k.invoke=function(a){return this.ze.call.apply(this.ze,[new pd(this,a)].concat(ta(xa.apply(1,arguments))))};k.Mb=function(a){var b=xa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ta(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};
k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};k.Ta=function(){this.Ca=!0};k.tb=function(){return this.Ca};var qd=function(a,b){od.call(this,a,b)};ra(qd,od);var rd=function(a,b){od.call(this,a,b)};ra(rd,od);var pd=function(a,b){this.ze=a;this.K=b};
pd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ra(b,a):a};pd.prototype.getName=function(){return this.ze.getName()};pd.prototype.Dd=function(){return this.K.Dd()};var sd=function(){this.map=new Map};sd.prototype.set=function(a,b){this.map.set(a,b)};sd.prototype.get=function(a){return this.map.get(a)};var td=function(){this.keys=[];this.values=[]};td.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};td.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function ud(){try{return Map?new sd:new td}catch(a){return new td}};var vd=function(a){if(a instanceof vd)return a;if(kd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};vd.prototype.getValue=function(){return this.value};vd.prototype.toString=function(){return String(this.value)};var xd=function(a){this.promise=a;this.Ca=!1;this.aa=new Da;this.aa.set("then",wd(this));this.aa.set("catch",wd(this,!0));this.aa.set("finally",wd(this,!1,!0))};k=xd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};
var wd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new qd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof qd||(d=void 0);e instanceof qd||(e=void 0);var f=this.K.rb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new vd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new xd(h)})};xd.prototype.Ta=function(){this.Ca=!0};xd.prototype.tb=function(){return this.Ca};function yd(a,b,c){var d=ud(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof md){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof xd)return g.promise.then(function(v){return yd(v,b,1)},function(v){return Promise.reject(yd(v,b,1))});if(g instanceof Ua){var q={};d.set(g,q);e(g,q);return q}if(g instanceof qd){var r=function(){for(var v=
xa.apply(0,arguments),u=[],w=0;w<v.length;w++)u[w]=zd(v[w],b,c);var y=new Ka(b?b.Dd():new Fa);b&&y.Ld(b.sb());return f(g.invoke.apply(g,[y].concat(ta(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof vd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function zd(a,b,c){var d=ud(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||tb(g)){var m=new md;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(id(g)){var p=new Ua;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new qd("",function(){for(var v=xa.apply(0,arguments),u=[],w=0;w<v.length;w++)u[w]=yd(this.evaluate(v[w]),b,c);return f(this.K.kj()(g,g,u))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new vd(g)};return f(a)};var Ad={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof md)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new md(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new md(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new md(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ta(xa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=nd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new md(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=nd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ta(xa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ta(xa.apply(1,arguments)))}};var Bd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Cd=new Aa("break"),Dd=new Aa("continue");function Ed(a,b){return this.evaluate(a)+this.evaluate(b)}function Fd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Gd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof md))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=yd(f.get(0));try{return d.toString(h)}catch(u){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Bd.hasOwnProperty(e)){var m=2;m=1;var n=yd(f,void 0,m);return zd(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof md){if(d.has(e)){var p=d.get(String(e));if(p instanceof qd){var q=nd(f);return p.invoke.apply(p,[this.K].concat(ta(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(Ad.supportedMethods.indexOf(e)>=
0){var r=nd(f);return Ad[e].call.apply(Ad[e],[d,this.K].concat(ta(r)))}}if(d instanceof qd||d instanceof Ua||d instanceof xd){if(d.has(e)){var t=d.get(e);if(t instanceof qd){var v=nd(f);return t.invoke.apply(t,[this.K].concat(ta(v)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof qd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof vd&&e==="toString")return d.toString();throw Oa(Error("TypeError: Object has no '"+
e+"' property."));}function Hd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Id(){var a=xa.apply(0,arguments),b=this.K.rb(),c=Qa(b,a);if(c instanceof Aa)return c}function Jd(){return Cd}function Kd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Aa)return d}}
function Ld(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.sh(c,d)}}}function Md(){return Dd}function Nd(a,b){return new Aa(a,this.evaluate(b))}function Od(a,b){for(var c=xa.apply(2,arguments),d=new md,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ta(c));this.K.add(a,this.evaluate(g))}function Pd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Qd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof vd,f=d instanceof vd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Rd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Sd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Qa(f,d);if(g instanceof Aa){if(g.type==="break")break;if(g.type==="return")return g}}}
function Td(a,b,c){if(typeof b==="string")return Sd(a,function(){return b.length},function(f){return f},c);if(b instanceof Ua||b instanceof xd||b instanceof md||b instanceof qd){var d=b.wa(),e=d.length;return Sd(a,function(){return e},function(f){return d[f]},c)}}function Ud(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Td(function(h){g.set(d,h);return g},e,f)}
function Vd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Td(function(h){var m=g.rb();m.sh(d,h);return m},e,f)}function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Td(function(h){var m=g.rb();m.add(d,h);return m},e,f)}function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Yd(function(h){g.set(d,h);return g},e,f)}
function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Yd(function(h){var m=g.rb();m.sh(d,h);return m},e,f)}function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Yd(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function Yd(a,b,c){if(typeof b==="string")return Sd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof md)return Sd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function ae(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var v=f.get(t);r.add(v,q.get(v))}}var f=this.evaluate(a);if(!(f instanceof md))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.rb();for(e(g,m);Ra(m,b);){var n=Qa(m,h);if(n instanceof Aa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.rb();e(m,p);Ra(p,c);m=p}}
function be(a,b){var c=xa.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof md))throw Error("Error: non-List value given for Fn argument names.");return new qd(a,function(){return function(){var f=xa.apply(0,arguments),g=d.rb();g.sb()===void 0&&g.Ld(this.K.sb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new md(h));var r=Qa(g,c);if(r instanceof Aa)return r.type===
"return"?r.data:r}}())}function ce(a){var b=this.evaluate(a),c=this.K;if(de&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ee(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ua||d instanceof xd||d instanceof md||d instanceof qd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:ld(e)&&(c=d[e]);else if(d instanceof vd)return;return c}function fe(a,b){return this.evaluate(a)>this.evaluate(b)}function ge(a,b){return this.evaluate(a)>=this.evaluate(b)}
function he(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof vd&&(c=c.getValue());d instanceof vd&&(d=d.getValue());return c===d}function ie(a,b){return!he.call(this,a,b)}function je(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Qa(this.K,d);if(e instanceof Aa)return e}var de=!1;
function ke(a,b){return this.evaluate(a)<this.evaluate(b)}function le(a,b){return this.evaluate(a)<=this.evaluate(b)}function me(){for(var a=new md,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function ne(){for(var a=new Ua,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function oe(a,b){return this.evaluate(a)%this.evaluate(b)}
function pe(a,b){return this.evaluate(a)*this.evaluate(b)}function qe(a){return-this.evaluate(a)}function re(a){return!this.evaluate(a)}function se(a,b){return!Qd.call(this,a,b)}function te(){return null}function ue(a,b){return this.evaluate(a)||this.evaluate(b)}function ve(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function we(a){return this.evaluate(a)}function xe(){return xa.apply(0,arguments)}function ye(a){return new Aa("return",this.evaluate(a))}
function ze(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof qd||d instanceof md||d instanceof Ua)&&d.set(String(e),f);return f}function Ae(a,b){return this.evaluate(a)-this.evaluate(b)}
function Be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Aa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Aa&&(g.type==="return"||g.type==="continue")))return g}
function Ce(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function De(a){var b=this.evaluate(a);return b instanceof qd?"function":typeof b}function Ee(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Fe(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Qa(this.K,e);if(f instanceof Aa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Qa(this.K,e);if(g instanceof Aa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ge(a){return~Number(this.evaluate(a))}function He(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ie(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Je(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Ke(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Le(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Ne(){}
function Oe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Aa)return d}catch(h){if(!(h instanceof Na&&h.am))throw h;var e=this.K.rb();a!==""&&(h instanceof Na&&(h=h.xm),e.add(a,new vd(h)));var f=this.evaluate(c),g=Qa(e,f);if(g instanceof Aa)return g}}function Pe(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.am))throw f;c=f}var e=this.evaluate(b);if(e instanceof Aa)return e;if(c)throw c;if(d instanceof Aa)return d};var Re=function(){this.C=new Ta;Qe(this)};Re.prototype.execute=function(a){return this.C.Lj(a)};var Qe=function(a){var b=function(c,d){var e=new rd(String(c),d);e.Ta();var f=String(c);a.C.C.set(f,e);Pa.set(f,e)};b("map",ne);b("and",$c);b("contains",cd);b("equals",ad);b("or",bd);b("startsWith",dd);b("variable",ed)};Re.prototype.Ob=function(a){this.C.Ob(a)};var Te=function(){this.H=!1;this.C=new Ta;Se(this);this.H=!0};Te.prototype.execute=function(a){return Ue(this.C.Lj(a))};var Ve=function(a,b,c){return Ue(a.C.mo(b,c))};Te.prototype.Ta=function(){this.C.Ta()};
var Se=function(a){var b=function(c,d){var e=String(c),f=new rd(e,d);f.Ta();a.C.C.set(e,f);Pa.set(e,f)};b(0,Ed);b(1,Fd);b(2,Gd);b(3,Hd);b(56,Ke);b(57,He);b(58,Ge);b(59,Me);b(60,Ie);b(61,Je);b(62,Le);b(53,Id);b(4,Jd);b(5,Kd);b(68,Oe);b(52,Ld);b(6,Md);b(49,Nd);b(7,me);b(8,ne);b(9,Kd);b(50,Od);b(10,Pd);b(12,Qd);b(13,Rd);b(67,Pe);b(51,be);b(47,Ud);b(54,Vd);b(55,Wd);b(63,ae);b(64,Xd);b(65,Zd);b(66,$d);b(15,ce);b(16,ee);b(17,ee);b(18,fe);b(19,ge);b(20,he);b(21,ie);b(22,je);b(23,ke);b(24,le);b(25,oe);b(26,
pe);b(27,qe);b(28,re);b(29,se);b(45,te);b(30,ue);b(32,ve);b(33,ve);b(34,we);b(35,we);b(46,xe);b(36,ye);b(43,ze);b(37,Ae);b(38,Be);b(39,Ce);b(40,De);b(44,Ne);b(41,Ee);b(42,Fe)};Te.prototype.Dd=function(){return this.C.Dd()};Te.prototype.Ob=function(a){this.C.Ob(a)};Te.prototype.Xc=function(a){this.C.Xc(a)};
function Ue(a){if(a instanceof Aa||a instanceof qd||a instanceof md||a instanceof Ua||a instanceof xd||a instanceof vd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var We=function(a){this.message=a};function Xe(a){a.Hr=!0;return a};var Ye=Xe(function(a){return typeof a==="string"});function Ze(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new We("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function $e(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var af=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function bf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Ze(e)+c}a<<=2;d||(a|=32);return c=""+Ze(a|b)+c}
function cf(a,b){var c;var d=a.Wc,e=a.Uc;d===void 0?c="":(e||(e=0),c=""+bf(1,1)+Ze(d<<2|e));var f=a.Zl,g=a.Po,h="4"+c+(f?""+bf(2,1)+Ze(f):"")+(g?""+bf(12,1)+Ze(g):""),m,n=a.Mj;m=n&&af.test(n)?""+bf(3,2)+n:"";var p,q=a.Ij;p=q?""+bf(4,1)+Ze(q):"";var r;var t=a.ctid;if(t&&b){var v=bf(5,3),u=t.split("-"),w=u[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=u[1];r=""+v+Ze(1+y.length)+(a.om||0)+y}}else r="";var A=a.Bq,C=a.we,D=a.Ma,F=a.Lr,H=h+m+p+r+(A?""+bf(6,1)+Ze(A):"")+(C?""+bf(7,3)+Ze(C.length)+
C:"")+(D?""+bf(8,3)+Ze(D.length)+D:"")+(F?""+bf(9,3)+Ze(F.length)+F:""),L;var Q=a.bm;Q=Q===void 0?{}:Q;for(var ca=[],U=l(Object.keys(Q)),qa=U.next();!qa.done;qa=U.next()){var T=qa.value;ca[Number(T)]=Q[T]}if(ca.length){var Z=bf(10,3),Y;if(ca.length===0)Y=Ze(0);else{for(var V=[],ka=0,ia=!1,la=0;la<ca.length;la++){ia=!0;var Sa=la%6;ca[la]&&(ka|=1<<Sa);Sa===5&&(V.push(Ze(ka)),ka=0,ia=!1)}ia&&V.push(Ze(ka));Y=V.join("")}var Ya=Y;L=""+Z+Ze(Ya.length)+Ya}else L="";var Ga=a.ym,Xa=a.rq;return H+L+(Ga?""+
bf(11,3)+Ze(Ga.length)+Ga:"")+(Xa?""+bf(13,3)+Ze(Xa.length)+Xa:"")};var df=function(){function a(b){return{toString:function(){return b}}}return{Wm:a("consent"),dk:a("convert_case_to"),ek:a("convert_false_to"),fk:a("convert_null_to"),gk:a("convert_true_to"),hk:a("convert_undefined_to"),Nq:a("debug_mode_metadata"),Ra:a("function"),Ei:a("instance_name"),po:a("live_only"),qo:a("malware_disabled"),METADATA:a("metadata"),uo:a("original_activity_id"),ir:a("original_vendor_template_id"),hr:a("once_on_load"),so:a("once_per_event"),Bl:a("once_per_load"),kr:a("priority_override"),
nr:a("respected_consent_types"),Kl:a("setup_tags"),qh:a("tag_id"),Sl:a("teardown_tags")}}();var Af;var Bf=[],Cf=[],Df=[],Ef=[],Ff=[],Gf,Hf,Jf;function Kf(a){Jf=Jf||a}
function Lf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Bf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Ef.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Df.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Mf(p[r])}Cf.push(p)}}
function Mf(a){}var Nf,Of=[],Pf=[];function Qf(a,b){var c={};c[df.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Rf(a,b,c){try{return Hf(Sf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Sf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Tf(a[e],b,c));return d},Tf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Tf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Bf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[df.Ei]);try{var m=Sf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Uf(m,{event:b,index:f,type:2,
name:h});Nf&&(d=Nf.So(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Tf(a[n],b,c)]=Tf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Tf(a[q],b,c);Jf&&(p=p||Jf.Sp(r));d.push(r)}return Jf&&p?Jf.Xo(d):d.join("");case "escape":d=Tf(a[1],b,c);if(Jf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Jf.Tp(a))return Jf.jq(d);d=String(d);for(var t=2;t<a.length;t++)lf[a[t]]&&(d=lf[a[t]](d));return d;
case "tag":var v=a[1];if(!Ef[v])throw Error("Unable to resolve tag reference "+v+".");return{hm:a[2],index:v};case "zb":var u={arg0:a[2],arg1:a[3],ignore_case:a[5]};u[df.Ra]=a[1];var w=Rf(u,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Uf=function(a,b){var c=a[df.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Gf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Of.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Eb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Bf[q];break;case 1:r=Ef[q];break;default:n="";break a}var t=r&&r[df.Ei];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var v,u,w;if(f&&Pf.indexOf(c)===-1){Pf.push(c);
var y=zb();v=e(g);var A=zb()-y,C=zb();u=Af(c,h,b);w=A-(zb()-C)}else if(e&&(v=e(g)),!e||f)u=Af(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),kd(v)?(Array.isArray(v)?Array.isArray(u):id(v)?id(u):typeof v==="function"?typeof u==="function":v===u)||d.reportMacroDiscrepancy(d.id,c):v!==u&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?v:u};var Vf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Vf,Error);Vf.prototype.getMessage=function(){return this.message};function Wf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Wf(a[c],b[c])}};function Xf(){return function(a,b){var c;var d=Yf;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Yf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)mb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Zf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=$f(a),f=0;f<Cf.length;f++){var g=Cf[f],h=ag(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Ef.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function ag(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function $f(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Rf(Df[c],a));return b[c]}};function bg(a,b){b[df.dk]&&typeof a==="string"&&(a=b[df.dk]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(df.fk)&&a===null&&(a=b[df.fk]);b.hasOwnProperty(df.hk)&&a===void 0&&(a=b[df.hk]);b.hasOwnProperty(df.gk)&&a===!0&&(a=b[df.gk]);b.hasOwnProperty(df.ek)&&a===!1&&(a=b[df.ek]);return a};var cg=function(){this.C={}},eg=function(a,b){var c=dg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ta(xa.apply(0,arguments)))})};function fg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Vf(c,d,g);}}
function gg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ta(xa.apply(1,arguments))));fg(e,b,d,g);fg(f,b,d,g)}}}};var kg=function(){var a=data.permissions||{},b=hg.ctid,c=this;this.H={};this.C=new cg;var d={},e={},f=gg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ta(xa.apply(1,arguments)))):{}});sb(a,function(g,h){function m(p){var q=xa.apply(1,arguments);if(!n[p])throw ig(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ta(q)))}var n={};sb(h,function(p,q){var r=jg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Xl&&!e[p]&&(e[p]=r.Xl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw ig(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var v=e[p];v&&v.apply(null,[m].concat(ta(t.slice(1))))}})},lg=function(a){return dg.H[a]||function(){}};
function jg(a,b){var c=Qf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=ig;try{return Uf(c)}catch(d){return{assert:function(e){throw new Vf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Vf(a,{},"Permission "+a+" is unknown.");}}}}function ig(a,b,c){return new Vf(a,b,c)};var mg=!1;var ng={};ng.Pm=vb('');ng.jp=vb('');
var rg=function(a){var b={},c=0;sb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(og.hasOwnProperty(e))b[og[e]]=g;else if(pg.hasOwnProperty(e)){var h=pg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=qg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];sb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
og={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},pg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},qg=["ca",
"c2","c3","c4","c5"];function sg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var tg=[];function ug(a){switch(a){case 1:return 0;case 38:return 13;case 53:return 1;case 54:return 2;case 52:return 7;case 211:return 18;case 75:return 3;case 103:return 14;case 197:return 15;case 203:return 16;case 114:return 12;case 115:return 4;case 116:return 5;case 209:return 17;case 135:return 9;case 136:return 6}}function vg(a,b){tg[a]=b;var c=ug(a);c!==void 0&&(Ha[c]=b)}function B(a){vg(a,!0)}
B(39);B(34);B(35);B(36);
B(56);B(145);B(153);B(144);B(120);
B(5);B(111);B(139);
B(87);B(92);B(159);
B(132);B(20);B(72);
B(113);B(154);B(116);B(143);
vg(23,!1),B(24);Ia[1]=sg('1',6E4);Ia[3]=sg('10',1);Ia[2]=sg('',50);
B(29);wg(26,25);B(37);
B(9);B(91);B(123);
B(158);B(71);
B(136);B(127);
B(27);B(69);
B(135);B(95);B(38);
B(103);B(112);B(63);B(152);
B(101);
B(122);B(121);
B(108);B(134);
B(115);B(31);B(22);
B(97);B(19);B(28);B(90);

B(59);B(13);
B(175);B(176);
B(183);B(185);B(187);B(192);
B(200);B(201);

function E(a){return!!tg[a]}
function wg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};
var xg=function(){this.events=[];this.C="";this.ra={};this.baseUrl="";this.N=0;this.P=this.H=!1;this.endpoint=0;E(89)&&(this.P=!0)};xg.prototype.add=function(a){return this.R(a)?(this.events.push(a),this.C=a.H,this.ra=a.ra,this.baseUrl=a.baseUrl,this.N+=a.P,this.H=a.N,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ba=a.eventId,this.ka=a.priorityId,!0):!1};xg.prototype.R=function(a){return this.events.length?this.events.length>=20||a.P+this.N>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.N&&this.Ba(a):!0};xg.prototype.Ba=function(a){var b=this;if(!this.P)return this.C===a.H;var c=Object.keys(this.ra);return c.length===Object.keys(a.ra).length&&c.every(function(d){return a.ra.hasOwnProperty(d)&&String(b.ra[d])===String(a.ra[d])})};var yg={},zg=(yg.uaa=!0,yg.uab=!0,yg.uafvl=!0,yg.uamb=!0,yg.uam=!0,yg.uap=!0,yg.uapv=!0,yg.uaw=!0,yg);
var Cg=function(a,b){var c=a.events;if(c.length===1)return Ag(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)sb(c[f].Md,function(t,v){v!=null&&(e[t]=e[t]||{},e[t][String(v)]=e[t][String(v)]+1||1)});var g={};sb(e,function(t,v){var u,w=-1,y=0;sb(v,function(A,C){y+=C;var D=(A.length+t.length+2)*(C-1);D>w&&(u=A,w=D)});y===c.length&&(g[t]=u)});Bg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={zj:void 0},p++){var q=[];n.zj={};sb(c[p].Md,function(t){return function(v,
u){g[v]!==""+u&&(t.zj[v]=u)}}(n));c[p].C&&q.push(c[p].C);Bg(n.zj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Ag=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Bg(a.Md,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Bg=function(a,b){sb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Dg=function(a){var b=[];sb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Eg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.ra=a.ra;this.Md=a.Md;this.ij=a.ij;this.N=d;this.H=Dg(a.ra);this.C=Dg(a.ij);this.P=this.C.length;if(e&&this.P>16384)throw Error("EVENT_TOO_LARGE");};
var Hg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Fg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Gg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Eb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Gg=/^[a-z$_][\w-$]*$/i,Fg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Ig=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Jg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Kg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Lg=new rb;function Mg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Lg.get(e);f||(f=new RegExp(b,d),Lg.set(e,f));return f.test(a)}catch(g){return!1}}function Ng(a,b){return String(a).indexOf(String(b))>=0}
function Og(a,b){return String(a)===String(b)}function Pg(a,b){return Number(a)>=Number(b)}function Qg(a,b){return Number(a)<=Number(b)}function Rg(a,b){return Number(a)>Number(b)}function Sg(a,b){return Number(a)<Number(b)}function Tg(a,b){return Eb(String(a),String(b))};var $g=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ah={Fn:"function",PixieMap:"Object",List:"Array"};
function bh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=$g.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof qd?n="Fn":m instanceof md?n="List":m instanceof Ua?n="PixieMap":m instanceof xd?n="PixiePromise":m instanceof vd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ah[n]||n)+", which does not match required type ")+
((ah[h]||h)+"."));}}}function G(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof qd?d.push("function"):g instanceof md?d.push("Array"):g instanceof Ua?d.push("Object"):g instanceof xd?d.push("Promise"):g instanceof vd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ch(a){return a instanceof Ua}function dh(a){return ch(a)||a===null||eh(a)}
function fh(a){return a instanceof qd}function gh(a){return fh(a)||a===null||eh(a)}function hh(a){return a instanceof md}function ih(a){return a instanceof vd}function jh(a){return typeof a==="string"}function kh(a){return jh(a)||a===null||eh(a)}function lh(a){return typeof a==="boolean"}function mh(a){return lh(a)||eh(a)}function nh(a){return lh(a)||a===null||eh(a)}function oh(a){return typeof a==="number"}function eh(a){return a===void 0};function ph(a){return""+a}
function qh(a,b){var c=[];return c};function rh(a,b){var c=new qd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Ta();return c}
function sh(a,b){var c=new Ua,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];jb(e)?c.set(d,rh(a+"_"+d,e)):id(e)?c.set(d,sh(a+"_"+d,e)):(mb(e)||lb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ta();return c};function th(a,b){if(!jh(a))throw G(this.getName(),["string"],arguments);if(!kh(b))throw G(this.getName(),["string","undefined"],arguments);var c={},d=new Ua;return d=sh("AssertApiSubject",
c)};function uh(a,b){if(!kh(b))throw G(this.getName(),["string","undefined"],arguments);if(a instanceof xd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ua;return d=sh("AssertThatSubject",c)};function vh(a){return function(){for(var b=xa.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(yd(b[e],d));return zd(a.apply(null,c))}}function wh(){for(var a=Math,b=xh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=vh(a[e].bind(a)))}return c};function yh(a){return a!=null&&Eb(a,"__cvt_")};function zh(a){var b;return b};function Ah(a){var b;if(!jh(a))throw G(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Bh(a){try{return encodeURI(a)}catch(b){}};function Ch(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Dh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Eh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Dh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Dh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Gh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Eh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Fh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Fh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Gh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Mg(d(c[0]),d(c[1]),!1);case 5:return Og(d(c[0]),d(c[1]));case 6:return Tg(d(c[0]),d(c[1]));case 7:return Jg(d(c[0]),d(c[1]));case 8:return Ng(d(c[0]),d(c[1]));case 9:return Sg(d(c[0]),d(c[1]));case 10:return Qg(d(c[0]),d(c[1]));case 11:return Rg(d(c[0]),d(c[1]));case 12:return Pg(d(c[0]),d(c[1]));case 13:return Kg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Hh(a){if(!kh(a))throw G(this.getName(),["string|undefined"],arguments);};function Ih(a,b){if(!oh(a)||!oh(b))throw G(this.getName(),["number","number"],arguments);return pb(a,b)};function Jh(){return(new Date).getTime()};function Kh(a){if(a===null)return"null";if(a instanceof md)return"array";if(a instanceof qd)return"function";if(a instanceof vd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Lh(a){function b(c){return function(d){try{return c(d)}catch(e){(mg||ng.Pm)&&a.call(this,e.message)}}}return{parse:b(function(c){return zd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(yd(c))}),publicName:"JSON"}};function Mh(a){return ub(yd(a,this.K))};function Nh(a){return Number(yd(a,this.K))};function Oh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Ph(a,b,c){var d=null,e=!1;return e?d:null};var xh="floor ceil round max min abs pow sqrt".split(" ");function Qh(){var a={};return{vp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Km:function(b,c){a[b]=c},reset:function(){a={}}}}function Rh(a,b){return function(){return qd.prototype.invoke.apply(a,[b].concat(ta(xa.apply(0,arguments))))}}
function Sh(a,b){if(!jh(a))throw G(this.getName(),["string","any"],arguments);}
function Th(a,b){if(!jh(a)||!ch(b))throw G(this.getName(),["string","PixieMap"],arguments);};var Uh={};var Vh=function(a){var b=new Ua;if(a instanceof md)for(var c=a.wa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof qd)for(var f=a.wa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Uh.keys=function(a){bh(this.getName(),arguments);if(a instanceof md||a instanceof qd||typeof a==="string")a=Vh(a);if(a instanceof Ua||a instanceof xd)return new md(a.wa());return new md};
Uh.values=function(a){bh(this.getName(),arguments);if(a instanceof md||a instanceof qd||typeof a==="string")a=Vh(a);if(a instanceof Ua||a instanceof xd)return new md(a.ac());return new md};
Uh.entries=function(a){bh(this.getName(),arguments);if(a instanceof md||a instanceof qd||typeof a==="string")a=Vh(a);if(a instanceof Ua||a instanceof xd)return new md(a.Jb().map(function(b){return new md(b)}));return new md};
Uh.freeze=function(a){(a instanceof Ua||a instanceof xd||a instanceof md||a instanceof qd)&&a.Ta();return a};Uh.delete=function(a,b){if(a instanceof Ua&&!a.tb())return a.remove(b),!0;return!1};function I(a,b){var c=xa.apply(2,arguments),d=a.K.sb();if(!d)throw Error("Missing program state.");if(d.oq){try{d.Yl.apply(null,[b].concat(ta(c)))}catch(e){throw db("TAGGING",21),e;}return}d.Yl.apply(null,[b].concat(ta(c)))};var Wh=function(){this.H={};this.C={};this.N=!0;};Wh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Wh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Wh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:jb(b)?rh(a,b):sh(a,b)};function Xh(a,b){var c=void 0;return c};function Yh(){var a={};return a};var J={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ia:"analytics_storage",jc:"region",ja:"consent_updated",vg:"wait_for_update",kn:"app_remove",ln:"app_store_refund",mn:"app_store_subscription_cancel",nn:"app_store_subscription_convert",on:"app_store_subscription_renew",pn:"consent_update",lk:"add_payment_info",mk:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",nk:"view_cart",Zc:"begin_checkout",Rd:"select_item",mc:"view_item_list",Ic:"select_promotion",nc:"view_promotion",
lb:"purchase",Sd:"refund",xb:"view_item",pk:"add_to_wishlist",qn:"exception",rn:"first_open",sn:"first_visit",qa:"gtag.config",Cb:"gtag.get",tn:"in_app_purchase",bd:"page_view",un:"screen_view",vn:"session_start",wn:"source_update",xn:"timing_complete",yn:"track_social",Td:"user_engagement",zn:"user_id_update",Le:"gclid_link_decoration_source",Me:"gclid_storage_source",oc:"gclgb",mb:"gclid",qk:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",za:"ads_data_redaction",Ne:"gad_source",Oe:"gad_source_src",
dd:"gclid_url",rk:"gclsrc",Pe:"gbraid",Xd:"wbraid",Ea:"allow_ad_personalization_signals",Cg:"allow_custom_scripts",Qe:"allow_direct_google_requests",Dg:"allow_display_features",Eg:"allow_enhanced_conversions",Pb:"allow_google_signals",nb:"allow_interest_groups",An:"app_id",Bn:"app_installer_id",Cn:"app_name",Dn:"app_version",Qb:"auid",En:"auto_detection_enabled",ed:"aw_remarketing",Th:"aw_remarketing_only",Fg:"discount",Gg:"aw_feed_country",Hg:"aw_feed_language",sa:"items",Ig:"aw_merchant_id",sk:"aw_basket_type",
Re:"campaign_content",Se:"campaign_id",Te:"campaign_medium",Ue:"campaign_name",Ve:"campaign",We:"campaign_source",Xe:"campaign_term",Rb:"client_id",tk:"rnd",Uh:"consent_update_type",Gn:"content_group",Hn:"content_type",Sb:"conversion_cookie_prefix",Ye:"conversion_id",Oa:"conversion_linker",Vh:"conversion_linker_disabled",fd:"conversion_api",Jg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",yb:"cookie_flags",gd:"cookie_name",Tb:"cookie_path",eb:"cookie_prefix",Jc:"cookie_update",Yd:"country",
Ua:"currency",Wh:"customer_buyer_stage",Ze:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",af:"custom_map",Zh:"gcldc",hd:"dclid",uk:"debug_mode",oa:"developer_id",In:"disable_merchant_reported_purchases",jd:"dc_custom_params",Jn:"dc_natural_search",vk:"dynamic_event_settings",wk:"affiliation",Kg:"checkout_option",ai:"checkout_step",xk:"coupon",bf:"item_list_name",bi:"list_name",Kn:"promotions",cf:"shipping",di:"tax",Lg:"engagement_time_msec",Mg:"enhanced_client_id",ei:"enhanced_conversions",
yk:"enhanced_conversions_automatic_settings",Ng:"estimated_delivery_date",fi:"euid_logged_in_state",df:"event_callback",Ln:"event_category",Ub:"event_developer_id_string",Mn:"event_label",kd:"event",Og:"event_settings",Pg:"event_timeout",Nn:"description",On:"fatal",Pn:"experiments",gi:"firebase_id",Zd:"first_party_collection",Qg:"_x_20",rc:"_x_19",zk:"fledge_drop_reason",Ak:"fledge",Bk:"flight_error_code",Ck:"flight_error_message",Dk:"fl_activity_category",Ek:"fl_activity_group",hi:"fl_advertiser_id",
Fk:"fl_ar_dedupe",ef:"match_id",Gk:"fl_random_number",Hk:"tran",Ik:"u",Rg:"gac_gclid",ae:"gac_wbraid",Jk:"gac_wbraid_multiple_conversions",Kk:"ga_restrict_domain",ii:"ga_temp_client_id",Qn:"ga_temp_ecid",ld:"gdpr_applies",Lk:"geo_granularity",Kc:"value_callback",sc:"value_key",uc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Mk:"google_tld",ff:"gpp_sid",hf:"gpp_string",Sg:"groups",Nk:"gsa_experiment_id",jf:"gtag_event_feature_usage",Ok:"gtm_up",Lc:"iframe_state",kf:"ignore_referrer",
ji:"internal_traffic_results",Pk:"_is_fpm",Mc:"is_legacy_converted",Nc:"is_legacy_loaded",Tg:"is_passthrough",md:"_lps",zb:"language",Ug:"legacy_developer_id_string",Pa:"linker",de:"accept_incoming",vc:"decorate_forms",ma:"domains",Oc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Qk:"method",Rn:"name",Rk:"navigation_type",lf:"new_customer",Vg:"non_interaction",Sn:"optimize_id",Sk:"page_hostname",nf:"page_path",Va:"page_referrer",Db:"page_title",Tk:"passengers",
Uk:"phone_conversion_callback",Tn:"phone_conversion_country_code",Vk:"phone_conversion_css_class",Un:"phone_conversion_ids",Wk:"phone_conversion_number",Xk:"phone_conversion_options",Vn:"_platinum_request_status",Wn:"_protected_audience_enabled",pf:"quantity",Wg:"redact_device_info",ki:"referral_exclusion_definition",Qq:"_request_start_time",Wb:"restricted_data_processing",Xn:"retoken",Yn:"sample_rate",li:"screen_name",Pc:"screen_resolution",Yk:"_script_source",Zn:"search_term",qb:"send_page_view",
nd:"send_to",od:"server_container_url",qf:"session_duration",Xg:"session_engaged",mi:"session_engaged_time",wc:"session_id",Yg:"session_number",rf:"_shared_user_id",tf:"delivery_postal_code",Rq:"_tag_firing_delay",Sq:"_tag_firing_time",Tq:"temporary_client_id",ni:"_timezone",oi:"topmost_url",ao:"tracking_id",ri:"traffic_type",Wa:"transaction_id",xc:"transport_url",Zk:"trip_type",rd:"update",Eb:"url_passthrough",al:"uptgs",uf:"_user_agent_architecture",vf:"_user_agent_bitness",wf:"_user_agent_full_version_list",
xf:"_user_agent_mobile",yf:"_user_agent_model",zf:"_user_agent_platform",Af:"_user_agent_platform_version",Bf:"_user_agent_wow64",fb:"user_data",si:"user_data_auto_latency",ui:"user_data_auto_meta",wi:"user_data_auto_multi",xi:"user_data_auto_selectors",yi:"user_data_auto_status",yc:"user_data_mode",Zg:"user_data_settings",Qa:"user_id",Xb:"user_properties",bl:"_user_region",Cf:"us_privacy_string",Fa:"value",fl:"wbraid_multiple_conversions",ud:"_fpm_parameters",Ci:"_host_name",rl:"_in_page_command",
sl:"_ip_override",wl:"_is_passthrough_cid",zc:"non_personalized_ads",Oi:"_sst_parameters",qc:"conversion_label",Aa:"page_location",Vb:"global_developer_id_string",pd:"tc_privacy_string"}};var Zh={},$h=(Zh[J.m.ja]="gcu",Zh[J.m.oc]="gclgb",Zh[J.m.mb]="gclaw",Zh[J.m.qk]="gclid_len",Zh[J.m.Ud]="gclgs",Zh[J.m.Vd]="gcllp",Zh[J.m.Wd]="gclst",Zh[J.m.Qb]="auid",Zh[J.m.Fg]="dscnt",Zh[J.m.Gg]="fcntr",Zh[J.m.Hg]="flng",Zh[J.m.Ig]="mid",Zh[J.m.sk]="bttype",Zh[J.m.Rb]="gacid",Zh[J.m.qc]="label",Zh[J.m.fd]="capi",Zh[J.m.Jg]="pscdl",Zh[J.m.Ua]="currency_code",Zh[J.m.Wh]="clobs",Zh[J.m.Ze]="vdltv",Zh[J.m.Xh]="clolo",Zh[J.m.Yh]="clolb",Zh[J.m.uk]="_dbg",Zh[J.m.Ng]="oedeld",Zh[J.m.Ub]="edid",Zh[J.m.zk]=
"fdr",Zh[J.m.Ak]="fledge",Zh[J.m.Rg]="gac",Zh[J.m.ae]="gacgb",Zh[J.m.Jk]="gacmcov",Zh[J.m.ld]="gdpr",Zh[J.m.Vb]="gdid",Zh[J.m.be]="_ng",Zh[J.m.ff]="gpp_sid",Zh[J.m.hf]="gpp",Zh[J.m.Nk]="gsaexp",Zh[J.m.jf]="_tu",Zh[J.m.Lc]="frm",Zh[J.m.Tg]="gtm_up",Zh[J.m.md]="lps",Zh[J.m.Ug]="did",Zh[J.m.ee]="fcntr",Zh[J.m.fe]="flng",Zh[J.m.he]="mid",Zh[J.m.lf]=void 0,Zh[J.m.Db]="tiba",Zh[J.m.Wb]="rdp",Zh[J.m.wc]="ecsid",Zh[J.m.rf]="ga_uid",Zh[J.m.tf]="delopc",Zh[J.m.pd]="gdpr_consent",Zh[J.m.Wa]="oid",Zh[J.m.al]=
"uptgs",Zh[J.m.uf]="uaa",Zh[J.m.vf]="uab",Zh[J.m.wf]="uafvl",Zh[J.m.xf]="uamb",Zh[J.m.yf]="uam",Zh[J.m.zf]="uap",Zh[J.m.Af]="uapv",Zh[J.m.Bf]="uaw",Zh[J.m.si]="ec_lat",Zh[J.m.ui]="ec_meta",Zh[J.m.wi]="ec_m",Zh[J.m.xi]="ec_sel",Zh[J.m.yi]="ec_s",Zh[J.m.yc]="ec_mode",Zh[J.m.Qa]="userId",Zh[J.m.Cf]="us_privacy",Zh[J.m.Fa]="value",Zh[J.m.fl]="mcov",Zh[J.m.Ci]="hn",Zh[J.m.rl]="gtm_ee",Zh[J.m.zc]="npa",Zh[J.m.Ye]=null,Zh[J.m.Pc]=null,Zh[J.m.zb]=null,Zh[J.m.sa]=null,Zh[J.m.Aa]=null,Zh[J.m.Va]=null,Zh[J.m.oi]=
null,Zh[J.m.ud]=null,Zh[J.m.Le]=null,Zh[J.m.Me]=null,Zh[J.m.uc]=null,Zh);function ai(a,b){if(a){var c=a.split("x");c.length===2&&(bi(b,"u_w",c[0]),bi(b,"u_h",c[1]))}}
function ci(a){var b=di;b=b===void 0?ei:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(fi(q.value)),r.push(fi(q.quantity)),r.push(fi(q.item_id)),r.push(fi(q.start_date)),r.push(fi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ei(a){return gi(a.item_id,a.id,a.item_name)}function gi(){for(var a=l(xa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function hi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function bi(a,b,c){c===void 0||c===null||c===""&&!zg[b]||(a[b]=c)}function fi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ii={},ki={tq:ji};function li(a,b){var c=ii[b],d=c.Mm;if(!(ii[b].active||ii[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;ki.tq(a,b)}}function ji(a,b){var c=ii[b];if(!(pb(0,9999)<c.percent*2*100))return a;mi(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function mi(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=pb(0,1)===0,e=pb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var K={J:{Vj:"call_conversion",W:"conversion",bo:"floodlight",Ef:"ga_conversion",Ki:"landing_page",Ga:"page_view",na:"remarketing",Sa:"user_data_lead",Ja:"user_data_web"}};function pi(a){return qi?z.querySelectorAll(a):null}
function ri(a,b){if(!qi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var si=!1;
if(z.querySelectorAll)try{var ti=z.querySelectorAll(":root");ti&&ti.length==1&&ti[0]==z.documentElement&&(si=!0)}catch(a){}var qi=si;function ui(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function vi(){this.blockSize=-1};function wi(a,b){this.blockSize=-1;this.blockSize=64;this.N=ya.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=ya.Int32Array?new Int32Array(64):Array(64);xi===void 0&&(ya.Int32Array?xi=new Int32Array(yi):xi=yi);this.reset()}za(wi,vi);for(var zi=[],Ai=0;Ai<63;Ai++)zi[Ai]=0;var Bi=[].concat(128,zi);
wi.prototype.reset=function(){this.P=this.H=0;var a;if(ya.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ci=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,v=a.C[6]|0,u=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(u+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&v)+(xi[w]|0)|0)+(c[w]|0)|0)|0;u=v;v=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+v|0;a.C[7]=a.C[7]+u|0};
wi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ci(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Ci(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};wi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Bi,56-this.H):this.update(Bi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Ci(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var yi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],xi;function Di(){wi.call(this,8,Ei)}za(Di,wi);var Ei=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Fi=/^[0-9A-Fa-f]{64}$/;function Gi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Hi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=x.crypto)==null?0:b.subtle){if(Fi.test(a))return Promise.resolve(a);try{var c=Gi(a);return x.crypto.subtle.digest("SHA-256",c).then(function(d){return Ii(d,x)}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ii(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ji=[],Ki;function Li(a){Ki?Ki(a):Ji.push(a)}function Mi(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Li(a),b):c}function Ni(a,b){if(!E(190))return b;var c=Oi(a,"");return c!==b?(Li(a),b):c}function Oi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Pi(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Li(a),b)}function Qi(){Ki=Ri;for(var a=l(Ji),b=a.next();!b.done;b=a.next())Ki(b.value);Ji.length=0};var Si={gn:'512',hn:'100',jn:'1000',ho:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',io:'US-CO',Do:Ni(44,'101509157~102015666~103116026~103200004~103233427~103308216~103308218~103351869~103351871~104684208~104684211~104718208~104784387~104784389~104885889~104885891')},Ti={cp:Number(Si.gn)||0,ep:Number(Si.hn)||0,hp:Number(Si.jn)||0,zp:Si.ho.split("~"),Ap:Si.io.split("~"),Kq:Si.Do};Object.assign({},Ti);function M(a){db("GTM",a)};
var Yi=function(a,b){var c=["tv.1"],d=Ui(a);if(d)return c.push(d),{Za:!1,Nj:c.join("~"),rg:{}};var e={},f=0;var g=Vi(a,function(p,q,r){var t=p.value,v;if(r){var u=q+"__"+f++;v="${userData."+u+"|sha256}";e[u]=t}else v=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+v)}).Za;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{Za:g,Nj:h,rg:m,fp:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Wi():Xi()}:{Za:g,Nj:h,rg:m}},$i=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=Zi(a);return Vi(b,function(){}).Za},Vi=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=aj[g.name];if(h){var m=bj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{Za:d,oj:c}},bj=function(a){var b=cj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(dj.test(e)||
Fi.test(e))}return d},cj=function(a){return ej.indexOf(a)!==-1},Xi=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BAVbRMqUOiJUQLIinBC3YVZcD9vNRXKwHiFYJlOwvV3oWdq6MPmHrYHH3Tsb4ArLTG2yrq9GR2+ngbmMaPzzZAI\x3d\x22,\x22version\x22:0},\x22id\x22:\x22f13fb26f-adc9-4df0-922e-d1cd05f94e51\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BC8F8UNF0Lt6iZosdyVL93v42+R8xwRnKtLiDw2ck6H/dR9X1HNdkZB6KzzXdoj06uZz1jIddA02tofa7AABhaA\x3d\x22,\x22version\x22:0},\x22id\x22:\x22ebff13c1-8294-4f85-87da-39aa2f9658c2\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BJoZFhiTSB5LuGZOcrxFF2IfcxKMhEzJAcnPZUh2VQDy5sN28BXf/ix29/P+8W/hjDGq/6VnvZUpf3NMTGbD8R4\x3d\x22,\x22version\x22:0},\x22id\x22:\x22c81a7607-ad9d-44c8-892f-a56fc2d0f53a\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BGRGKUQTzvTvmyJOfyvxkd9kM1OWbNuNvw1MQGczwHUCkzVD08LsTxn46XzwBe7viDdzcC8BopySeDSJFjsS6Xc\x3d\x22,\x22version\x22:0},\x22id\x22:\x227d68cd60-2fce-40c7-b59c-59f400022449\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BFYk2nokkvPJdLz7H6jjcrrGR7+bPmBQtlXh4riP4Q8cJAbUDKsoLmRZ99mq5NoEk8Mmsl6x1z9Tix0vd4FhrTA\x3d\x22,\x22version\x22:0},\x22id\x22:\x2213f6b2d0-1bfb-4d3e-8a3e-e5c5d7ada173\x22}]}'},hj=function(a){if(x.Promise){var b=void 0;return b}},mj=function(a,b,c,d,e){if(x.Promise)try{var f=Zi(a),g=ij(f,e).then(jj);return g}catch(p){}},oj=function(a){try{return jj(nj(Zi(a)))}catch(b){}},gj=function(a,b){var c=void 0;return c},jj=function(a){var b=a.Vc,c=a.time,d=["tv.1"],e=Ui(b);if(e)return d.push(e),{Lb:encodeURIComponent(d.join("~")),oj:!1,Za:!1,time:c,nj:!0};var f=b.filter(function(n){return!bj(n)}),g=Vi(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.oj,m=g.Za;return{Lb:encodeURIComponent(d.join("~")),oj:h,Za:m,time:c,nj:!1}},Ui=function(a){if(a.length===1&&a[0].name==="error_code")return aj.error_code+
"."+a[0].value},lj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(aj[d.name]&&d.value)return!0}return!1},Zi=function(a){function b(r,t,v,u){var w=pj(r);w!==""&&(Fi.test(w)?h.push({name:t,value:w,index:u}):h.push({name:t,value:v(w),index:u}))}function c(r,t){var v=r;if(lb(v)||Array.isArray(v)){v=nb(r);for(var u=0;u<v.length;++u){var w=pj(v[u]),y=Fi.test(w);t&&!y&&M(89);!t&&y&&M(88)}}}function d(r,t){var v=r[t];c(v,!1);var u=
qj[t];r[u]&&(r[t]&&M(90),v=r[u],c(v,!0));return v}function e(r,t,v){for(var u=nb(d(r,t)),w=0;w<u.length;++w)b(u[w],t,v)}function f(r,t,v,u){var w=d(r,t);b(w,t,v,u)}function g(r){return function(t){M(64);return r(t)}}var h=[];if(x.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",rj);e(a,"phone_number",sj);e(a,"first_name",g(tj));e(a,"last_name",g(tj));var m=a.home_address||{};e(m,"street",g(uj));e(m,"city",g(uj));e(m,"postal_code",g(vj));e(m,"region",
g(uj));e(m,"country",g(vj));for(var n=nb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",tj,p);f(q,"last_name",tj,p);f(q,"street",uj,p);f(q,"city",uj,p);f(q,"postal_code",vj,p);f(q,"region",uj,p);f(q,"country",vj,p)}return h},wj=function(a){var b=a?Zi(a):[];return jj({Vc:b})},xj=function(a){return a&&a!=null&&Object.keys(a).length>0&&x.Promise?Zi(a).some(function(b){return b.value&&cj(b.name)&&!Fi.test(b.value)}):!1},pj=function(a){return a==null?"":lb(a)?xb(String(a)):"e0"},vj=function(a){return a.replace(yj,
"")},tj=function(a){return uj(a.replace(/\s/g,""))},uj=function(a){return xb(a.replace(zj,"").toLowerCase())},sj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Aj.test(a)?a:"e0"},rj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Bj.test(c))return c}return"e0"},nj=function(a){var b=Wc();try{a.forEach(function(e){if(e.value&&cj(e.name)){var f;var g=e.value,h=x;if(g===""||
g==="e0"||Fi.test(g))f=g;else try{var m=new Di;m.update(Gi(g));f=Ii(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Vc:a};if(b!==void 0){var d=Wc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Vc:[]}}},ij=function(a,b){if(!a.some(function(d){return d.value&&cj(d.name)}))return Promise.resolve({Vc:a});if(!x.Promise)return Promise.resolve({Vc:[]});var c=b?Wc():void 0;return Promise.all(a.map(function(d){return d.value&&cj(d.name)?Hi(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Vc:a};if(c!==void 0){var e=Wc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Vc:[]}})},zj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Bj=/^\S+@\S+\.\S+$/,Aj=/^\+\d{10,15}$/,yj=/[.~]/g,dj=/^[0-9A-Za-z_-]{43}$/,Cj={},aj=(Cj.email="em",Cj.phone_number="pn",Cj.first_name="fn",Cj.last_name="ln",Cj.street="sa",Cj.city="ct",Cj.region="rg",Cj.country="co",Cj.postal_code="pc",Cj.error_code="ec",Cj),Dj={},qj=(Dj.email="sha256_email_address",Dj.phone_number="sha256_phone_number",
Dj.first_name="sha256_first_name",Dj.last_name="sha256_last_name",Dj.street="sha256_street",Dj);var ej=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Ej={},Fj=(Ej[J.m.nb]=1,Ej[J.m.od]=2,Ej[J.m.xc]=2,Ej[J.m.za]=3,Ej[J.m.Ze]=4,Ej[J.m.Cg]=5,Ej[J.m.Jc]=6,Ej[J.m.eb]=6,Ej[J.m.ob]=6,Ej[J.m.gd]=6,Ej[J.m.Tb]=6,Ej[J.m.yb]=6,Ej[J.m.pb]=7,Ej[J.m.Wb]=9,Ej[J.m.Dg]=10,Ej[J.m.Pb]=11,Ej),Gj={},Hj=(Gj.unknown=13,Gj.standard=14,Gj.unique=15,Gj.per_session=16,Gj.transactions=17,Gj.items_sold=18,Gj);var fb=[];function Ij(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Fj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Fj[f],h=b;h=h===void 0?!1:h;db("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(fb[g]=!0)}}};var Jj=function(){this.C=new Set;this.H=new Set},Lj=function(a){var b=Kj.R;a=a===void 0?[]:a;var c=[].concat(ta(b.C)).concat([].concat(ta(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Mj=function(){var a=[].concat(ta(Kj.R.C));a.sort(function(b,c){return b-c});return a},Nj=function(){var a=Kj.R,b=Ti.Kq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Oj={},Pj=Ni(14,"56p1"),Qj=Pi(15,Number("0")),Rj=Ni(19,"dataLayer");Ni(20,"");Ni(16,"ChAI8MiDwwYQkZSL9vqbxeViEiUAdZJiVdh3E/CEa/KwKjQpGemsJ48Zi5J4Wa0Zr1/HMDRoIyGEGgIjgQ\x3d\x3d");var Sj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Tj={__paused:1,__tg:1},Uj;for(Uj in Sj)Sj.hasOwnProperty(Uj)&&(Tj[Uj]=1);var Vj=Mi(11,vb("true")),Wj=!1,Xj,Yj=!1;Yj=!0;
Xj=Yj;var Zj,ak=!1;Zj=ak;Oj.Ag=Ni(3,"www.googletagmanager.com");var bk=""+Oj.Ag+(Xj?"/gtag/js":"/gtm.js"),ck=null,dk=null,ek={},fk={};Oj.Xm=Mi(2,vb("true"));var gk="";Oj.Pi=gk;
var Kj=new function(){this.R=new Jj;this.C=this.N=!1;this.H=0;this.Ba=this.Xa=this.Fb=this.P="";this.ba=this.ka=!1};function hk(){var a;a=a===void 0?[]:a;return Lj(a).join("~")}function ik(){var a=Kj.P.length;return Kj.P[a-1]==="/"?Kj.P.substring(0,a-1):Kj.P}function jk(){return Kj.C?E(84)?Kj.H===0:Kj.H!==1:!1}function kk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var lk=new rb,mk={},nk={},qk={name:Rj,set:function(a,b){jd(Gb(a,b),mk);ok()},get:function(a){return pk(a,2)},reset:function(){lk=new rb;mk={};ok()}};function pk(a,b){return b!=2?lk.get(a):rk(a)}function rk(a,b){var c=a.split(".");b=b||[];for(var d=mk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function sk(a,b){nk.hasOwnProperty(a)||(lk.set(a,b),jd(Gb(a,b),mk),ok())}
function tk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=pk(c,1);if(Array.isArray(d)||id(d))d=jd(d,null);nk[c]=d}}function ok(a){sb(nk,function(b,c){lk.set(b,c);jd(Gb(b),mk);jd(Gb(b,c),mk);a&&delete nk[b]})}function uk(a,b){var c,d=(b===void 0?2:b)!==1?rk(a):lk.get(a);gd(d)==="array"||gd(d)==="object"?c=jd(d,null):c=d;return c};
var wk=function(a){for(var b=[],c=Object.keys(vk),d=0;d<c.length;d++){var e=c[d],f=vk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},xk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},yk=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!Eb(w,"#")&&!Eb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(Eb(m,"dataLayer."))f=pk(m.substring(10));
else{var n=m.split(".");f=x[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&qi)try{var q=pi(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Lc(q[r])||xb(q[r].value));f=f.length===1?f[0]:f}}catch(w){M(149)}if(E(60)){for(var t,v=0;v<g.length&&(t=pk(g[v]),t===void 0);v++);var u=f!==void 0;d[b]=xk(t!==void 0,u);u||(f=t)}return f?(a[b]=f,!0):!1},zk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=yk(c,"email",
a.email,b)||d;d=yk(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=yk(g,"first_name",e[f].first_name,b)||d;d=yk(g,"last_name",e[f].last_name,b)||d;d=yk(g,"street",e[f].street,b)||d;d=yk(g,"city",e[f].city,b)||d;d=yk(g,"region",e[f].region,b)||d;d=yk(g,"country",e[f].country,b)||d;d=yk(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},Ak=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&id(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&db("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return zk(a[J.m.yk])}},Bk=function(a){return id(a)?!!a.enable_code:!1},vk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Ek=/:[0-9]+$/,Fk=/^\d+\.fls\.doubleclick\.net$/;function Gk(a,b,c,d){var e=Hk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Hk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=sa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Ik(a){try{return decodeURIComponent(a)}catch(b){}}function Jk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Kk(a.protocol)||Kk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Ek,"").toLowerCase());return Lk(a,b,c,d,e)}
function Lk(a,b,c,d,e){var f,g=Kk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Mk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Ek,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||db("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Gk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Kk(a){return a?a.replace(":","").toLowerCase():""}function Mk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Nk={},Ok=0;
function Pk(a){var b=Nk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||db("TAGGING",1),d="/"+d);var e=c.hostname.replace(Ek,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Ok<5&&(Nk[a]=b,Ok++)}return b}function Qk(a,b,c){var d=Pk(a);return Lb(b,d,c)}
function Rk(a){var b=Pk(x.location.href),c=Jk(b,"host",!1);if(c&&c.match(Fk)){var d=Jk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Sk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Tk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Uk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Pk(""+c+b).href}}function Vk(a,b){if(jk()||Kj.N)return Uk(a,b)}
function Wk(){return!!Oj.Pi&&Oj.Pi.split("@@").join("")!=="SGTM_TOKEN"}function Xk(a){for(var b=l([J.m.od,J.m.xc]),c=b.next();!c.done;c=b.next()){var d=N(a,c.value);if(d)return d}}function Yk(a,b,c){c=c===void 0?"":c;if(!jk())return a;var d=b?Sk[a]||"":"";d==="/gs"&&(c="");return""+ik()+d+c}function Zk(a){if(!jk())return a;for(var b=l(Tk),c=b.next();!c.done;c=b.next())if(Eb(a,""+ik()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function $k(a){var b=String(a[df.Ra]||"").replace(/_/g,"");return Eb(b,"cvt")?"cvt":b}var al=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var bl={qq:Pi(27,Number("0.005000")),ap:Pi(42,Number("0.010000"))},cl=Math.random(),dl=al||cl<Number(bl.qq),el=al||cl>=1-Number(bl.ap);var fl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},gl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var hl,il;a:{for(var jl=["CLOSURE_FLAGS"],kl=ya,ll=0;ll<jl.length;ll++)if(kl=kl[jl[ll]],kl==null){il=null;break a}il=kl}var ml=il&&il[610401301];hl=ml!=null?ml:!1;function nl(){var a=ya.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var ol,pl=ya.navigator;ol=pl?pl.userAgentData||null:null;function ql(a){if(!hl||!ol)return!1;for(var b=0;b<ol.brands.length;b++){var c=ol.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function rl(a){return nl().indexOf(a)!=-1};function sl(){return hl?!!ol&&ol.brands.length>0:!1}function tl(){return sl()?!1:rl("Opera")}function ul(){return rl("Firefox")||rl("FxiOS")}function vl(){return sl()?ql("Chromium"):(rl("Chrome")||rl("CriOS"))&&!(sl()?0:rl("Edge"))||rl("Silk")};var wl=function(a){wl[" "](a);return a};wl[" "]=function(){};var xl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function yl(){return hl?!!ol&&!!ol.platform:!1}function zl(){return rl("iPhone")&&!rl("iPod")&&!rl("iPad")}function Al(){zl()||rl("iPad")||rl("iPod")};tl();sl()||rl("Trident")||rl("MSIE");rl("Edge");!rl("Gecko")||nl().toLowerCase().indexOf("webkit")!=-1&&!rl("Edge")||rl("Trident")||rl("MSIE")||rl("Edge");nl().toLowerCase().indexOf("webkit")!=-1&&!rl("Edge")&&rl("Mobile");yl()||rl("Macintosh");yl()||rl("Windows");(yl()?ol.platform==="Linux":rl("Linux"))||yl()||rl("CrOS");yl()||rl("Android");zl();rl("iPad");rl("iPod");Al();nl().toLowerCase().indexOf("kaios");var Bl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{wl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Cl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Dl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},El=function(a){if(x.top==x)return 0;if(a===void 0?0:a){var b=x.location.ancestorOrigins;
if(b)return b[b.length-1]==x.location.origin?1:2}return Bl(x.top)?1:2},Fl=function(a){a=a===void 0?document:a;return a.createElement("img")},Gl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Bl(a)&&(b=a);return b};function Hl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Il(){return Hl("join-ad-interest-group")&&jb(qc.joinAdInterestGroup)}
function Jl(a,b,c){var d=Ia[3]===void 0?1:Ia[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ia[2]===void 0?50:Ia[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&zb()-q<(Ia[1]===void 0?6E4:Ia[1])?(db("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Kl(f[0]);else{if(n)return db("TAGGING",10),!1}else f.length>=d?Kl(f[0]):n&&Kl(m[0]);Ec(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:zb()});return!0}function Kl(a){try{a.parentNode.removeChild(a)}catch(b){}};function Ll(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Ml=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};ul();zl()||rl("iPod");rl("iPad");!rl("Android")||vl()||ul()||tl()||rl("Silk");vl();!rl("Safari")||vl()||(sl()?0:rl("Coast"))||tl()||(sl()?0:rl("Edge"))||(sl()?ql("Microsoft Edge"):rl("Edg/"))||(sl()?ql("Opera"):rl("OPR"))||ul()||rl("Silk")||rl("Android")||Al();var Nl={},Ol=null,Pl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Ol){Ol={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Nl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Ol[q]===void 0&&(Ol[q]=p)}}}for(var r=Nl[f],t=Array(Math.floor(b.length/3)),v=r[64]||"",u=0,w=0;u<b.length-2;u+=3){var y=b[u],
A=b[u+1],C=b[u+2],D=r[y>>2],F=r[(y&3)<<4|A>>4],H=r[(A&15)<<2|C>>6],L=r[C&63];t[w++]=""+D+F+H+L}var Q=0,ca=v;switch(b.length-u){case 2:Q=b[u+1],ca=r[(Q&15)<<2]||v;case 1:var U=b[u];t[w]=""+r[U>>2]+r[(U&3)<<4|Q>>4]+ca+v}return t.join("")};var Ql=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Rl=/#|$/,Sl=function(a,b){var c=a.search(Rl),d=Ql(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return xl(a.slice(d,e!==-1?e:0))},Tl=/[?&]($|#)/,Ul=function(a,b,c){for(var d,e=a.search(Rl),f=0,g,h=[];(g=Ql(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Tl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),v;t<0||t>r?(t=r,v=""):v=d.substring(t+1,r);q=[d.slice(0,t),v,d.slice(r)];var u=q[1];q[1]=p?u?u+"&"+p:p:u;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Vl(a,b,c,d,e,f){var g=Sl(c,"fmt");if(d){var h=Sl(c,"random"),m=Sl(c,"label")||"";if(!h)return!1;var n=Pl(xl(m)+":"+xl(h));if(!Ll(a,n,d))return!1}g&&Number(g)!==4&&(c=Ul(c,"rfmt",g));var p=Ul(c,"fmt",4);Cc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Wl={},Xl=(Wl[1]={},Wl[2]={},Wl[3]={},Wl[4]={},Wl);function Yl(a,b,c){var d=Zl(b,c);if(d){var e=Xl[b][d];e||(e=Xl[b][d]=[]);e.push(Object.assign({},a))}}function $l(a,b){var c=Zl(a,b);if(c){var d=Xl[a][c];d&&(Xl[a][c]=d.filter(function(e){return!e.Gm}))}}function am(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Zl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function bm(a){var b=xa.apply(1,arguments);el&&(Yl(a,2,b[0]),Yl(a,3,b[0]));Oc.apply(null,ta(b))}function cm(a){var b=xa.apply(1,arguments);el&&Yl(a,2,b[0]);return Pc.apply(null,ta(b))}function dm(a){var b=xa.apply(1,arguments);el&&Yl(a,3,b[0]);Fc.apply(null,ta(b))}
function em(a){var b=xa.apply(1,arguments),c=b[0];el&&(Yl(a,2,c),Yl(a,3,c));return Sc.apply(null,ta(b))}function fm(a){var b=xa.apply(1,arguments);el&&Yl(a,1,b[0]);Cc.apply(null,ta(b))}function gm(a){var b=xa.apply(1,arguments);b[0]&&el&&Yl(a,4,b[0]);Ec.apply(null,ta(b))}function hm(a){var b=xa.apply(1,arguments);el&&Yl(a,1,b[2]);return Vl.apply(null,ta(b))}function im(a){var b=xa.apply(1,arguments);el&&Yl(a,4,b[0]);Jl.apply(null,ta(b))};var jm=/gtag[.\/]js/,km=/gtm[.\/]js/,lm=!1;function mm(a){if(lm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(jm.test(c))return"3";if(km.test(c))return"2"}return"0"};function nm(a,b,c){var d=om();pm().container[a]={state:1,context:b,parent:d};qm({ctid:a,isDestination:!1},c)}function qm(a,b){var c=pm();c.pending||(c.pending=[]);ob(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function rm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var sm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=rm()};
function pm(){var a=uc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new sm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=rm());return c};var tm={},hg={ctid:Ni(5,"G-15FN2QGQ72"),canonicalContainerId:Ni(6,"165554359"),zm:Ni(10,"G-15FN2QGQ72|GT-WRFB6G4"),Am:Ni(9,"G-15FN2QGQ72")};tm.pe=Mi(7,vb(""));function um(){return tm.pe&&vm().some(function(a){return a===hg.ctid})}function wm(){return hg.canonicalContainerId||"_"+hg.ctid}function xm(){return hg.zm?hg.zm.split("|"):[hg.ctid]}
function vm(){return hg.Am?hg.Am.split("|").filter(function(a){return E(108)?a.indexOf("GTM-")!==0:!0}):[]}function ym(){var a=zm(om()),b=a&&a.parent;if(b)return zm(b)}function Am(){var a=zm(om());if(a){for(;a.parent;){var b=zm(a.parent);if(!b)break;a=b}return a}}function zm(a){var b=pm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Bm(){var a=pm();if(a.pending){for(var b,c=[],d=!1,e=xm(),f=vm(),g={},h=0;h<a.pending.length;g={mg:void 0},h++)g.mg=a.pending[h],ob(g.mg.target.isDestination?f:e,function(m){return function(n){return n===m.mg.target.ctid}}(g))?d||(b=g.mg.onLoad,d=!0):c.push(g.mg);a.pending=c;if(b)try{b(wm())}catch(m){}}}
function Cm(){for(var a=hg.ctid,b=xm(),c=vm(),d=function(n,p){var q={canonicalContainerId:hg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};sc&&(q.scriptElement=sc);tc&&(q.scriptSource=tc);if(ym()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var v,u=(v=q.scriptElement)==null?void 0:v.src;if(u){for(var w=Kj.C,y=Pk(u),A=w?y.pathname:""+y.hostname+y.pathname,C=z.scripts,D="",F=0;F<C.length;++F){var H=C[F];if(!(H.innerHTML.length===
0||!w&&H.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||H.innerHTML.indexOf(A)<0)){if(H.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}D=String(F)}}if(D){t=D;break b}}t=void 0}var L=t;if(L){lm=!0;r=L;break a}}var Q=[].slice.call(z.scripts);r=q.scriptElement?String(Q.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=mm(q)}var ca=p?e.destination:e.container,U=ca[n];U?(p&&U.state===0&&M(93),Object.assign(U,q)):ca[n]=q},e=pm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[wm()]={};Bm()}function Dm(){var a=wm();return!!pm().canonical[a]}function Em(a){return!!pm().container[a]}function Fm(a){var b=pm().destination[a];return!!b&&!!b.state}function om(){return{ctid:hg.ctid,isDestination:tm.pe}}function Gm(){var a=pm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Hm(){var a={};sb(pm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Im(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Jm(){for(var a=pm(),b=l(xm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Km={Ia:{je:0,oe:1,Li:2}};Km.Ia[Km.Ia.je]="FULL_TRANSMISSION";Km.Ia[Km.Ia.oe]="LIMITED_TRANSMISSION";Km.Ia[Km.Ia.Li]="NO_TRANSMISSION";var Lm={X:{Gb:0,Da:1,Hc:2,Qc:3}};Lm.X[Lm.X.Gb]="NO_QUEUE";Lm.X[Lm.X.Da]="ADS";Lm.X[Lm.X.Hc]="ANALYTICS";Lm.X[Lm.X.Qc]="MONITORING";function Mm(){var a=uc("google_tag_data",{});return a.ics=a.ics||new Nm}var Nm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Nm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;db("TAGGING",19);b==null?db("TAGGING",18):Om(this,a,b==="granted",c,d,e,f,g)};Nm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Om(this,a[d],void 0,void 0,"","",b,c)};
var Om=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&lb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(db("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Nm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Pm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Pm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&lb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,ze:b})};var Pm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Bm=!0)}};Nm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Bm){d.Bm=!1;try{d.ze({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Qm=!1,Rm=!1,Sm={},Tm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Sm.ad_storage=1,Sm.analytics_storage=1,Sm.ad_user_data=1,Sm.ad_personalization=1,Sm),usedContainerScopedDefaults:!1};function Um(a){var b=Mm();b.accessedAny=!0;return(lb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Tm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Vm(a){var b=Mm();b.accessedAny=!0;return b.getConsentState(a,Tm)}function Wm(a){var b=Mm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function Xm(){if(!Ja(8))return!1;var a=Mm();a.accessedAny=!0;if(a.active)return!0;if(!Tm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Tm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Tm.containerScopedDefaults[c.value]!==1)return!0;return!1}function Ym(a,b){Mm().addListener(a,b)}
function Zm(a,b){Mm().notifyListeners(a,b)}function $m(a,b){function c(){for(var e=0;e<b.length;e++)if(!Wm(b[e]))return!0;return!1}if(c()){var d=!1;Ym(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function an(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Um(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=lb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Ym(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var bn={},cn=(bn[Lm.X.Gb]=Km.Ia.je,bn[Lm.X.Da]=Km.Ia.je,bn[Lm.X.Hc]=Km.Ia.je,bn[Lm.X.Qc]=Km.Ia.je,bn),dn=function(a,b){this.C=a;this.consentTypes=b};dn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Um(a)});case 1:return this.consentTypes.some(function(a){return Um(a)});default:ic(this.C,"consentsRequired had an unknown type")}};
var en={},fn=(en[Lm.X.Gb]=new dn(0,[]),en[Lm.X.Da]=new dn(0,["ad_storage"]),en[Lm.X.Hc]=new dn(0,["analytics_storage"]),en[Lm.X.Qc]=new dn(1,["ad_storage","analytics_storage"]),en);var hn=function(a){var b=this;this.type=a;this.C=[];Ym(fn[a].consentTypes,function(){gn(b)||b.flush()})};hn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var gn=function(a){return cn[a.type]===Km.Ia.Li&&!fn[a.type].isConsentGranted()},jn=function(a,b){gn(a)?a.C.push(b):b()},kn=new Map;function ln(a){kn.has(a)||kn.set(a,new hn(a));return kn.get(a)};var mn={Z:{Um:"aw_user_data_cache",Ph:"cookie_deprecation_label",Bg:"diagnostics_page_id",co:"fl_user_data_cache",fo:"ga4_user_data_cache",Ff:"ip_geo_data_cache",Fi:"ip_geo_fetch_in_progress",Al:"nb_data",wo:"page_experiment_ids",Of:"pt_data",Cl:"pt_listener_set",Jl:"service_worker_endpoint",Ll:"shared_user_id",Ml:"shared_user_id_requested",ph:"shared_user_id_source"}};var nn=function(a){return Xe(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(mn.Z);
function on(a,b){b=b===void 0?!1:b;if(nn(a)){var c,d,e=(d=(c=uc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function pn(a,b){var c=on(a,!0);c&&c.set(b)}function qn(a){var b;return(b=on(a))==null?void 0:b.get()}function rn(a){var b={},c=on(a);if(!c){c=on(a,!0);if(!c)return;c.set(b)}return c.get()}function sn(a,b){if(typeof b==="function"){var c;return(c=on(a,!0))==null?void 0:c.subscribe(b)}}function tn(a,b){var c=on(a);return c?c.unsubscribe(b):!1};var un="https://"+Ni(21,"www.googletagmanager.com"),vn="/td?id="+hg.ctid,wn={},xn=(wn.tdp=1,wn.exp=1,wn.pid=1,wn.dl=1,wn.seq=1,wn.t=1,wn.v=1,wn),yn=["mcc"],zn={},An={},Bn=!1,Cn=void 0;function Dn(a,b,c){An[a]=b;(c===void 0||c)&&En(a)}function En(a,b){zn[a]!==void 0&&(b===void 0||!b)||Eb(hg.ctid,"GTM-")&&a==="mcc"||(zn[a]=!0)}
function Fn(a){a=a===void 0?!1:a;var b=Object.keys(zn).filter(function(c){return zn[c]===!0&&An[c]!==void 0&&(a||!yn.includes(c))}).map(function(c){var d=An[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Yk(un)+vn+(""+b+"&z=0")}function Gn(){Object.keys(zn).forEach(function(a){xn[a]||(zn[a]=!1)})}
function Hn(a){a=a===void 0?!1:a;if(Kj.ba&&el&&hg.ctid){var b=ln(Lm.X.Qc);if(gn(b))Bn||(Bn=!0,jn(b,Hn));else{var c=Fn(a),d={destinationId:hg.ctid,endpoint:61};a?em(d,c,void 0,{Hh:!0},void 0,function(){dm(d,c+"&img=1")}):dm(d,c);Gn();Bn=!1}}}var In={};
function Jn(a){var b=String(a);In.hasOwnProperty(b)||(In[b]=!0,Dn("csp",Object.keys(In).join("~")),En("csp",!0),Cn===void 0&&E(171)&&(Cn=x.setTimeout(function(){var c=zn.csp;zn.csp=!0;zn.seq=!1;var d=Fn(!1);zn.csp=c;zn.seq=!0;Cc(d+"&script=1");Cn=void 0},500)))}function Kn(){Object.keys(zn).filter(function(a){return zn[a]&&!xn[a]}).length>0&&Hn(!0)}var Ln;
function Mn(){if(qn(mn.Z.Bg)===void 0){var a=function(){pn(mn.Z.Bg,pb());Ln=0};a();x.setInterval(a,864E5)}else sn(mn.Z.Bg,function(){Ln=0});Ln=0}function Nn(){Mn();Dn("v","3");Dn("t","t");Dn("pid",function(){return String(qn(mn.Z.Bg))});Dn("seq",function(){return String(++Ln)});Dn("exp",hk());Hc(x,"pagehide",Kn)};var On=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Pn=[J.m.od,J.m.xc,J.m.Zd,J.m.Rb,J.m.wc,J.m.Qa,J.m.Pa,J.m.eb,J.m.ob,J.m.Tb],Qn=!1,Rn=!1,Sn={},Tn={};function Un(){!Rn&&Qn&&(On.some(function(a){return Tm.containerScopedDefaults[a]!==1})||Vn("mbc"));Rn=!0}function Vn(a){el&&(Dn(a,"1"),Hn())}function Wn(a,b){if(!Sn[b]&&(Sn[b]=!0,Tn[b]))for(var c=l(Pn),d=c.next();!d.done;d=c.next())if(N(a,d.value)){Vn("erc");break}};function Xn(a){db("HEALTH",a)};var Yn={up:Ni(22,"eyIwIjoiSEsiLCIxIjoiIiwiMiI6ZmFsc2UsIjMiOiJnb29nbGUuY29tLmhrIiwiNCI6IiIsIjUiOnRydWUsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0")},Zn={},$n=!1;function ao(){function a(){c!==void 0&&tn(mn.Z.Ff,c);try{var e=qn(mn.Z.Ff);Zn=JSON.parse(e)}catch(f){M(123),Xn(2),Zn={}}$n=!0;b()}var b=bo,c=void 0,d=qn(mn.Z.Ff);d?a(d):(c=sn(mn.Z.Ff,a),co())}
function co(){function a(c){pn(mn.Z.Ff,c||"{}");pn(mn.Z.Fi,!1)}if(!qn(mn.Z.Fi)){pn(mn.Z.Fi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function eo(){var a=Yn.up;try{return JSON.parse(bb(a))}catch(b){return M(123),Xn(2),{}}}function fo(){return Zn["0"]||""}function go(){return Zn["1"]||""}function ho(){var a=!1;a=!!Zn["2"];return a}function io(){return Zn["6"]!==!1}function jo(){var a="";a=Zn["4"]||"";return a}
function ko(){var a=!1;a=!!Zn["5"];return a}function lo(){var a="";a=Zn["3"]||"";return a};var mo={},no=Object.freeze((mo[J.m.Ea]=1,mo[J.m.Dg]=1,mo[J.m.Eg]=1,mo[J.m.Pb]=1,mo[J.m.sa]=1,mo[J.m.ob]=1,mo[J.m.pb]=1,mo[J.m.yb]=1,mo[J.m.gd]=1,mo[J.m.Tb]=1,mo[J.m.eb]=1,mo[J.m.Jc]=1,mo[J.m.af]=1,mo[J.m.oa]=1,mo[J.m.vk]=1,mo[J.m.df]=1,mo[J.m.Og]=1,mo[J.m.Pg]=1,mo[J.m.Zd]=1,mo[J.m.Kk]=1,mo[J.m.uc]=1,mo[J.m.ce]=1,mo[J.m.Mk]=1,mo[J.m.Sg]=1,mo[J.m.ji]=1,mo[J.m.Mc]=1,mo[J.m.Nc]=1,mo[J.m.Pa]=1,mo[J.m.ki]=1,mo[J.m.Wb]=1,mo[J.m.qb]=1,mo[J.m.nd]=1,mo[J.m.od]=1,mo[J.m.qf]=1,mo[J.m.mi]=1,mo[J.m.tf]=1,mo[J.m.xc]=
1,mo[J.m.rd]=1,mo[J.m.Zg]=1,mo[J.m.Xb]=1,mo[J.m.ud]=1,mo[J.m.Oi]=1,mo));Object.freeze([J.m.Aa,J.m.Va,J.m.Db,J.m.zb,J.m.li,J.m.Qa,J.m.gi,J.m.Gn]);
var oo={},po=Object.freeze((oo[J.m.kn]=1,oo[J.m.ln]=1,oo[J.m.mn]=1,oo[J.m.nn]=1,oo[J.m.on]=1,oo[J.m.rn]=1,oo[J.m.sn]=1,oo[J.m.tn]=1,oo[J.m.vn]=1,oo[J.m.Td]=1,oo)),qo={},ro=Object.freeze((qo[J.m.lk]=1,qo[J.m.mk]=1,qo[J.m.Pd]=1,qo[J.m.Qd]=1,qo[J.m.nk]=1,qo[J.m.Zc]=1,qo[J.m.Rd]=1,qo[J.m.mc]=1,qo[J.m.Ic]=1,qo[J.m.nc]=1,qo[J.m.lb]=1,qo[J.m.Sd]=1,qo[J.m.xb]=1,qo[J.m.pk]=1,qo)),so=Object.freeze([J.m.Ea,J.m.Qe,J.m.Pb,J.m.Jc,J.m.Zd,J.m.kf,J.m.qb,J.m.rd]),to=Object.freeze([].concat(ta(so))),uo=Object.freeze([J.m.pb,
J.m.Pg,J.m.qf,J.m.mi,J.m.Lg]),vo=Object.freeze([].concat(ta(uo))),wo={},xo=(wo[J.m.U]="1",wo[J.m.ia]="2",wo[J.m.V]="3",wo[J.m.La]="4",wo),yo={},zo=Object.freeze((yo.search="s",yo.youtube="y",yo.playstore="p",yo.shopping="h",yo.ads="a",yo.maps="m",yo));function Ao(a){return typeof a!=="object"||a===null?{}:a}function Bo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Co(a){if(a!==void 0&&a!==null)return Bo(a)}function Do(a){return typeof a==="number"?a:Co(a)};function Eo(a){return a&&a.indexOf("pending:")===0?Fo(a.substr(8)):!1}function Fo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=zb();return b<c+3E5&&b>c-9E5};var Go=!1,Ho=!1,Io=!1,Jo=0,Ko=!1,Lo=[];function Mo(a){if(Jo===0)Ko&&Lo&&(Lo.length>=100&&Lo.shift(),Lo.push(a));else if(No()){var b=Ni(41,'google.tagmanager.ta.prodqueue'),c=uc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Oo(){Po();Ic(z,"TAProdDebugSignal",Oo)}function Po(){if(!Ho){Ho=!0;Qo();var a=Lo;Lo=void 0;a==null||a.forEach(function(b){Mo(b)})}}
function Qo(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Fo(a)?Jo=1:!Eo(a)||Go||Io?Jo=2:(Io=!0,Hc(z,"TAProdDebugSignal",Oo,!1),x.setTimeout(function(){Po();Go=!0},200))}function No(){if(!Ko)return!1;switch(Jo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Ro=!1;function So(a,b){var c=xm(),d=vm();if(No()){var e=To("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Mo(e)}}
function Uo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=No()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=To("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Mo(h)}}function Vo(a){No()&&Uo(a())}
function To(a,b){b=b===void 0?{}:b;b.groupId=Wo;var c,d=b,e={publicId:Xo};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=Ro?"OGT":"GTM";c.key.targetRef=Yo;return c}var Xo="",Yo={ctid:"",isDestination:!1},Wo;
function Zo(a){var b=hg.ctid,c=um();Jo=0;Ko=!0;Qo();Wo=a;Xo=b;Ro=Xj;Yo={ctid:b,isDestination:c}};var $o=[J.m.U,J.m.ia,J.m.V,J.m.La],ap,bp;function cp(a){var b=a[J.m.jc];b||(b=[""]);for(var c={dg:0};c.dg<b.length;c={dg:c.dg},++c.dg)sb(a,function(d){return function(e,f){if(e!==J.m.jc){var g=Bo(f),h=b[d.dg],m=fo(),n=go();Rm=!0;Qm&&db("TAGGING",20);Mm().declare(e,g,h,m,n)}}}(c))}
function dp(a){Un();!bp&&ap&&Vn("crc");bp=!0;var b=a[J.m.vg];b&&M(41);var c=a[J.m.jc];c?M(40):c=[""];for(var d={eg:0};d.eg<c.length;d={eg:d.eg},++d.eg)sb(a,function(e){return function(f,g){if(f!==J.m.jc&&f!==J.m.vg){var h=Co(g),m=c[e.eg],n=Number(b),p=fo(),q=go();n=n===void 0?0:n;Qm=!0;Rm&&db("TAGGING",20);Mm().default(f,h,m,p,q,n,Tm)}}}(d))}
function ep(a){Tm.usedContainerScopedDefaults=!0;var b=a[J.m.jc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(go())&&!c.includes(fo()))return}sb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Tm.usedContainerScopedDefaults=!0;Tm.containerScopedDefaults[d]=e==="granted"?3:2})}
function fp(a,b){Un();ap=!0;sb(a,function(c,d){var e=Bo(d);Qm=!0;Rm&&db("TAGGING",20);Mm().update(c,e,Tm)});Zm(b.eventId,b.priorityId)}function gp(a){a.hasOwnProperty("all")&&(Tm.selectedAllCorePlatformServices=!0,sb(zo,function(b){Tm.corePlatformServices[b]=a.all==="granted";Tm.usedCorePlatformServices=!0}));sb(a,function(b,c){b!=="all"&&(Tm.corePlatformServices[b]=c==="granted",Tm.usedCorePlatformServices=!0)})}function hp(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Um(b)})}
function ip(a,b){Ym(a,b)}function jp(a,b){an(a,b)}function kp(a,b){$m(a,b)}function lp(){var a=[J.m.U,J.m.La,J.m.V];Mm().waitForUpdate(a,500,Tm)}function mp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Mm().clearTimeout(d,void 0,Tm)}Zm()}function np(){if(!Zj)for(var a=io()?kk(Kj.Xa):kk(Kj.Fb),b=0;b<$o.length;b++){var c=$o[b],d=c,e=a[c]?"granted":"denied";Mm().implicit(d,e)}};var op=!1,pp=[];function qp(){if(!op){op=!0;for(var a=pp.length-1;a>=0;a--)pp[a]();pp=[]}};var rp=x.google_tag_manager=x.google_tag_manager||{};function sp(a,b){return rp[a]=rp[a]||b()}function tp(){var a=hg.ctid,b=up;rp[a]=rp[a]||b}function vp(){var a=rp.sequence||1;rp.sequence=a+1;return a};function wp(){if(rp.pscdl!==void 0)qn(mn.Z.Ph)===void 0&&pn(mn.Z.Ph,rp.pscdl);else{var a=function(c){rp.pscdl=c;pn(mn.Z.Ph,c)},b=function(){a("error")};try{qc.cookieDeprecationLabel?(a("pending"),qc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var xp=0;function yp(a){el&&a===void 0&&xp===0&&(Dn("mcc","1"),xp=1)};var zp={Df:{Ym:"cd",Zm:"ce",bn:"cf",dn:"cpf",fn:"cu"}};var Ap=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Bp=/\s/;
function Cp(a,b){if(lb(a)){a=xb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Ap.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Bp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Dp(a,b){for(var c={},d=0;d<a.length;++d){var e=Cp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Ep[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Fp={},Ep=(Fp[0]=0,Fp[1]=1,Fp[2]=2,Fp[3]=0,Fp[4]=1,Fp[5]=0,Fp[6]=0,Fp[7]=0,Fp);var Gp=Number('')||500,Hp={},Ip={},Jp={initialized:11,complete:12,interactive:13},Kp={},Lp=Object.freeze((Kp[J.m.qb]=!0,Kp)),Mp=void 0;function Np(a,b){if(b.length&&el){var c;(c=Hp)[a]!=null||(c[a]=[]);Ip[a]!=null||(Ip[a]=[]);var d=b.filter(function(e){return!Ip[a].includes(e)});Hp[a].push.apply(Hp[a],ta(d));Ip[a].push.apply(Ip[a],ta(d));!Mp&&d.length>0&&(En("tdc",!0),Mp=x.setTimeout(function(){Hn();Hp={};Mp=void 0},Gp))}}
function Op(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Pp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var v;gd(t)==="object"?v=t[r]:gd(t)==="array"&&(v=t[r]);return v===void 0?Lp[r]:v},f=Op(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=gd(m)==="object"||gd(m)==="array",q=gd(n)==="object"||gd(n)==="array";if(p&&q)Pp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Qp(){Dn("tdc",function(){Mp&&(x.clearTimeout(Mp),Mp=void 0);var a=[],b;for(b in Hp)Hp.hasOwnProperty(b)&&a.push(b+"*"+Hp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Rp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Sp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},N=function(a,b,c,d){for(var e=l(Sp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Tp=function(a){for(var b={},c=Sp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Rp.prototype.getMergedValues=function(a,b,c){function d(n){id(n)&&sb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Sp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Up=function(a){for(var b=[J.m.Ve,J.m.Re,J.m.Se,J.m.Te,J.m.Ue,J.m.We,J.m.Xe],c=Sp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Vp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Wp=function(a,
b){a.H=b;return a},Xp=function(a,b){a.R=b;return a},Yp=function(a,b){a.C=b;return a},Zp=function(a,b){a.N=b;return a},$p=function(a,b){a.ba=b;return a},aq=function(a,b){a.P=b;return a},bq=function(a,b){a.eventMetadata=b||{};return a},cq=function(a,b){a.onSuccess=b;return a},dq=function(a,b){a.onFailure=b;return a},eq=function(a,b){a.isGtmEvent=b;return a},fq=function(a){return new Rp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var O={A:{Sj:"accept_by_default",ug:"add_tag_timing",Lh:"allow_ad_personalization",Uj:"batch_on_navigation",Wj:"client_id_source",He:"consent_event_id",Ie:"consent_priority_id",Mq:"consent_state",ja:"consent_updated",Yc:"conversion_linker_enabled",ya:"cookie_options",xg:"create_dc_join",yg:"create_fpm_geo_join",zg:"create_fpm_signals_join",Od:"create_google_join",Ke:"em_event",Pq:"endpoint_for_debug",kk:"enhanced_client_id_source",Sh:"enhanced_match_result",ie:"euid_mode_enabled",hb:"event_start_timestamp_ms",
ml:"event_usage",bh:"extra_tag_experiment_ids",Wq:"add_parameter",Ai:"attribution_reporting_experiment",Bi:"counting_method",eh:"send_as_iframe",Xq:"parameter_order",fh:"parsed_target",eo:"ga4_collection_subdomain",pl:"gbraid_cookie_marked",fa:"hit_type",vd:"hit_type_override",ko:"is_config_command",Gf:"is_consent_update",Hf:"is_conversion",tl:"is_ecommerce",wd:"is_external_event",Gi:"is_fallback_aw_conversion_ping_allowed",If:"is_first_visit",vl:"is_first_visit_conversion",gh:"is_fl_fallback_conversion_flow_allowed",
ke:"is_fpm_encryption",hh:"is_fpm_split",me:"is_gcp_conversion",Hi:"is_google_signals_allowed",xd:"is_merchant_center",ih:"is_new_to_site",jh:"is_server_side_destination",ne:"is_session_start",xl:"is_session_start_conversion",ar:"is_sgtm_ga_ads_conversion_study_control_group",er:"is_sgtm_prehit",yl:"is_sgtm_service_worker",Ii:"is_split_conversion",lo:"is_syn",Jf:"join_id",Ji:"join_elapsed",Kf:"join_timer_sec",qe:"tunnel_updated",jr:"prehit_for_retry",lr:"promises",mr:"record_aw_latency",Ac:"redact_ads_data",
se:"redact_click_ids",xo:"remarketing_only",Hl:"send_ccm_parallel_ping",oh:"send_fledge_experiment",qr:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Ni:"send_to_targets",Il:"send_user_data_hit",ib:"source_canonical_id",Ha:"speculative",Nl:"speculative_in_message",Ol:"suppress_script_load",Pl:"syn_or_mod",Tl:"transient_ecsid",Qf:"transmission_type",jb:"user_data",vr:"user_data_from_automatic",wr:"user_data_from_automatic_getter",ue:"user_data_from_code",rh:"user_data_from_manual",Vl:"user_data_mode",
Rf:"user_id_updated"}};var gq={Tm:Number("5"),Nr:Number("")},hq=[],iq=!1;function jq(a){hq.push(a)}var kq="?id="+hg.ctid,lq=void 0,mq={},nq=void 0,oq=new function(){var a=5;gq.Tm>0&&(a=gq.Tm);this.H=a;this.C=0;this.N=[]},pq=1E3;
function qq(a,b){var c=lq;if(c===void 0)if(b)c=vp();else return"";for(var d=[Yk("https://www.googletagmanager.com"),"/a",kq],e=l(hq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function rq(){if(Kj.ba&&(nq&&(x.clearTimeout(nq),nq=void 0),lq!==void 0&&sq)){var a=ln(Lm.X.Qc);if(gn(a))iq||(iq=!0,jn(a,rq));else{var b;if(!(b=mq[lq])){var c=oq;b=c.C<c.H?!1:zb()-c.N[c.C%c.H]<1E3}if(b||pq--<=0)M(1),mq[lq]=!0;else{var d=oq,e=d.C++%d.H;d.N[e]=zb();var f=qq(!0);dm({destinationId:hg.ctid,endpoint:56,eventId:lq},f);iq=sq=!1}}}}function tq(){if(dl&&Kj.ba){var a=qq(!0,!0);dm({destinationId:hg.ctid,endpoint:56,eventId:lq},a)}}var sq=!1;
function uq(a){mq[a]||(a!==lq&&(rq(),lq=a),sq=!0,nq||(nq=x.setTimeout(rq,500)),qq().length>=2022&&rq())}var vq=pb();function wq(){vq=pb()}function xq(){return[["v","3"],["t","t"],["pid",String(vq)]]};var yq={};function zq(a,b,c){dl&&a!==void 0&&(yq[a]=yq[a]||[],yq[a].push(c+b),uq(a))}function Aq(a){var b=a.eventId,c=a.Nd,d=[],e=yq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete yq[b];return d};function Bq(a,b,c,d){var e=Cp(a,!0);e&&Cq.register(e,b,c,d)}function Dq(a,b,c,d){var e=Cp(c,d.isGtmEvent);e&&(Wj&&(d.deferrable=!0),Cq.push("event",[b,a],e,d))}function Eq(a,b,c,d){var e=Cp(c,d.isGtmEvent);e&&Cq.push("get",[a,b],e,d)}function Fq(a){var b=Cp(a,!0),c;b?c=Gq(Cq,b).C:c={};return c}function Hq(a,b){var c=Cp(a,!0);c&&Iq(Cq,c,b)}
var Jq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Kq=function(a,b,c,d){this.H=zb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Lq=function(){this.destinations={};this.C={};this.commands=[]},Gq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Jq},Mq=function(a,b,c,d){if(d.C){var e=Gq(a,d.C),f=e.ba;if(f){var g=jd(c,null),h=jd(e.R[d.C.id],null),m=jd(e.P,null),n=jd(e.C,null),p=jd(a.C,null),q={};if(dl)try{q=
jd(mk,null)}catch(w){M(72)}var r=d.C.prefix,t=function(w){zq(d.messageContext.eventId,r,w)},v=fq(eq(dq(cq(bq($p(Zp(aq(Yp(Xp(Wp(new Vp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),u=function(){try{zq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(el&&w==="config"){var A,C=(A=Cp(y))==null?void 0:A.ids;if(!(C&&C.length>1)){var D,F=uc("google_tag_data",{});F.td||(F.td={});D=F.td;var H=jd(v.P);jd(v.C,H);var L=[],Q;for(Q in D)D.hasOwnProperty(Q)&&Pp(D[Q],H).length&&L.push(Q);L.length&&(Np(y,L),db("TAGGING",Jp[z.readyState]||14));D[y]=H}}f(d.C.id,b,d.H,v)}catch(ca){zq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?u():jn(e.ka,u)}}};
Lq.prototype.register=function(a,b,c,d){var e=Gq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=ln(c),Iq(this,a,d||{}),this.flush())};
Lq.prototype.push=function(a,b,c,d){c!==void 0&&(Gq(this,c).status===1&&(Gq(this,c).status=2,this.push("require",[{}],c,{})),Gq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[O.A.Pf]||(d.eventMetadata[O.A.Pf]=[c.destinationId]),d.eventMetadata[O.A.Ni]||(d.eventMetadata[O.A.Ni]=[c.id]));this.commands.push(new Kq(a,c,b,d));d.deferrable||this.flush()};
Lq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Sc:void 0,xh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Gq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Gq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];sb(h,function(t,v){jd(Gb(t,v),b.C)});Ij(h,!0);break;case "config":var m=Gq(this,g);
e.Sc={};sb(f.args[0],function(t){return function(v,u){jd(Gb(v,u),t.Sc)}}(e));var n=!!e.Sc[J.m.rd];delete e.Sc[J.m.rd];var p=g.destinationId===g.id;Ij(e.Sc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Mq(this,J.m.qa,e.Sc,f);m.N=!0;p?jd(e.Sc,m.P):(jd(e.Sc,m.R[g.id]),M(70));d=!0;break;case "event":e.xh={};sb(f.args[0],function(t){return function(v,u){jd(Gb(v,u),t.xh)}}(e));Ij(e.xh);Mq(this,f.args[1],e.xh,f);break;case "get":var q={},r=(q[J.m.sc]=f.args[0],q[J.m.Kc]=f.args[1],q);Mq(this,J.m.Cb,r,f)}this.commands.shift();
Nq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Nq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Gq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Iq=function(a,b,c){var d=jd(c,null);jd(Gq(a,b).C,d);Gq(a,b).C=d},Cq=new Lq;function Oq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Pq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Qq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Fl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=nc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Pq(e,"load",f);Pq(e,"error",f)};Oq(e,"load",f);Oq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Rq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Cl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Sq(c,b)}
function Sq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Qq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Tq=function(){this.ba=this.ba;this.P=this.P};Tq.prototype.ba=!1;Tq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Tq.prototype[Symbol.dispose]=function(){this.dispose()};Tq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Tq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Uq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Vq=function(a,b){b=b===void 0?{}:b;Tq.call(this);this.C=null;this.ka={};this.Fb=0;this.R=null;this.H=a;var c;this.Xa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Cr)!=null?d:!1};ra(Vq,Tq);Vq.prototype.N=function(){this.ka={};this.R&&(Pq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Tq.prototype.N.call(this)};var Xq=function(a){return typeof a.H.__tcfapi==="function"||Wq(a)!=null};
Vq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=gl(function(){return a(c)}),e=0;this.Xa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Xa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Uq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{Yq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Vq.prototype.removeEventListener=function(a){a&&a.listenerId&&Yq(this,"removeEventListener",null,a.listenerId)};
var $q=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=Zq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&Zq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?Zq(a.purpose.legitimateInterests,
b)&&Zq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},Zq=function(a,b){return!(!a||!a[b])},Yq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Wq(a)){ar(a);var g=++a.Fb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Wq=function(a){if(a.C)return a.C;a.C=Dl(a.H,"__tcfapiLocator");return a.C},ar=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Oq(a.H,"message",b)}},br=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Uq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Rq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var cr={1:0,3:0,4:0,7:3,9:3,10:3};function dr(){return sp("tcf",function(){return{}})}var er=function(){return new Vq(x,{timeoutMs:-1})};
function fr(){var a=dr(),b=er();Xq(b)&&!gr()&&!hr()&&M(124);if(!a.active&&Xq(b)){gr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Mm().active=!0,a.tcString="tcunavailable");lp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)ir(a),mp([J.m.U,J.m.La,J.m.V]),Mm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,hr()&&(a.active=!0),!jr(c)||gr()||hr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in cr)cr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(jr(c)){var g={},h;for(h in cr)if(cr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={tp:!0};p=p===void 0?{}:p;m=br(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.tp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?$q(n,"1",0):!0:!1;g["1"]=m}else g[h]=$q(c,h,cr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(mp([J.m.U,J.m.La,J.m.V]),Mm().active=!0):(r[J.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":mp([J.m.V]),fp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:kr()||""}))}}else mp([J.m.U,J.m.La,J.m.V])})}catch(c){ir(a),mp([J.m.U,J.m.La,J.m.V]),Mm().active=!0}}}
function ir(a){a.type="e";a.tcString="tcunavailable"}function jr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function gr(){return x.gtag_enable_tcf_support===!0}function hr(){return dr().enableAdvertiserConsentMode===!0}function kr(){var a=dr();if(a.active)return a.tcString}function lr(){var a=dr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function mr(a){if(!cr.hasOwnProperty(String(a)))return!0;var b=dr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var nr=[J.m.U,J.m.ia,J.m.V,J.m.La],or={},pr=(or[J.m.U]=1,or[J.m.ia]=2,or);function qr(a){if(a===void 0)return 0;switch(N(a,J.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function rr(){return(E(183)?Ti.zp:Ti.Ap).indexOf(go())!==-1&&qc.globalPrivacyControl===!0}function sr(a){if(rr())return!1;var b=qr(a);if(b===3)return!1;switch(Vm(J.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function tr(){return Xm()||!Um(J.m.U)||!Um(J.m.ia)}function ur(){var a={},b;for(b in pr)pr.hasOwnProperty(b)&&(a[pr[b]]=Vm(b));return"G1"+$e(a[1]||0)+$e(a[2]||0)}var vr={},wr=(vr[J.m.U]=0,vr[J.m.ia]=1,vr[J.m.V]=2,vr[J.m.La]=3,vr);function xr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function yr(a){for(var b="1",c=0;c<nr.length;c++){var d=b,e,f=nr[c],g=Tm.delegatedConsentTypes[f];e=g===void 0?0:wr.hasOwnProperty(g)?12|wr[g]:8;var h=Mm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|xr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[xr(m.declare)<<4|xr(m.default)<<2|xr(m.update)])}var n=b,p=(rr()?1:0)<<3,q=(Xm()?1:0)<<2,r=qr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Tm.containerScopedDefaults.ad_storage<<4|Tm.containerScopedDefaults.analytics_storage<<2|Tm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Tm.usedContainerScopedDefaults?1:0)<<2|Tm.containerScopedDefaults.ad_personalization]}
function zr(){if(!Um(J.m.V))return"-";for(var a=Object.keys(zo),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Tm.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=zo[m])}(Tm.usedCorePlatformServices?Tm.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Ar(){return io()||(gr()||hr())&&lr()==="1"?"1":"0"}function Br(){return(io()?!0:!(!gr()&&!hr())&&lr()==="1")||!Um(J.m.V)}
function Cr(){var a="0",b="0",c;var d=dr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=dr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;io()&&(h|=1);lr()==="1"&&(h|=2);gr()&&(h|=4);var m;var n=dr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Mm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Dr(){return go()==="US-CO"};function Er(){var a=!1;return a};var Fr;function Gr(){if(tc===null)return 0;var a=Yc();if(!a)return 0;var b=a.getEntriesByName(tc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Hr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Ir(a){a=a===void 0?{}:a;var b=hg.ctid.split("-")[0].toUpperCase(),c={ctid:hg.ctid,Ij:Qj,Mj:Pj,om:tm.pe?2:1,Bq:a.Jm,we:hg.canonicalContainerId};if(E(210)){var d;c.rq=(d=Am())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.Po=(e=Fr)!=null?e:Fr=Gr()}c.we!==a.Ma&&(c.Ma=a.Ma);var f=ym();c.ym=f?f.canonicalContainerId:void 0;Xj?(c.Wc=Hr[b],c.Wc||(c.Wc=0)):c.Wc=Zj?13:10;Kj.C?(c.Uc=0,c.Zl=2):Kj.N?c.Uc=1:Er()?c.Uc=2:c.Uc=3;var g={6:!1};Kj.H===2?g[7]=!0:Kj.H===1&&(g[2]=!0);if(tc){var h=Jk(Pk(tc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.bm=g;return cf(c,a.th)}
function Jr(){if(!E(192))return Ir();if(E(193))return cf({Ij:Qj,Mj:Pj});var a=hg.ctid.split("-")[0].toUpperCase(),b={ctid:hg.ctid,Ij:Qj,Mj:Pj,om:tm.pe?2:1,we:hg.canonicalContainerId},c=ym();b.ym=c?c.canonicalContainerId:void 0;Xj?(b.Wc=Hr[a],b.Wc||(b.Wc=0)):b.Wc=Zj?13:10;Kj.C?(b.Uc=0,b.Zl=2):Kj.N?b.Uc=1:Er()?b.Uc=2:b.Uc=3;var d={6:!1};Kj.H===2?d[7]=!0:Kj.H===1&&(d[2]=!0);if(tc){var e=Jk(Pk(tc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.bm=d;return cf(b)};function Kr(a,b,c,d){var e,f=Number(a.Dc!=null?a.Dc:void 0);f!==0&&(e=new Date((b||zb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Fc:d}};var Lr=["ad_storage","ad_user_data"];function Mr(a,b){if(!a)return db("TAGGING",32),10;if(b===null||b===void 0||b==="")return db("TAGGING",33),11;var c=Nr(!1);if(c.error!==0)return db("TAGGING",34),c.error;if(!c.value)return db("TAGGING",35),2;c.value[a]=b;var d=Or(c);d!==0&&db("TAGGING",36);return d}
function Pr(a){if(!a)return db("TAGGING",27),{error:10};var b=Nr();if(b.error!==0)return db("TAGGING",29),b;if(!b.value)return db("TAGGING",30),{error:2};if(!(a in b.value))return db("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(db("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Nr(a){a=a===void 0?!0:a;if(!Um(Lr))return db("TAGGING",43),{error:3};try{if(!x.localStorage)return db("TAGGING",44),{error:1}}catch(f){return db("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return db("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return db("TAGGING",47),{error:12}}}catch(f){return db("TAGGING",48),{error:8}}if(b.schema!=="gcl")return db("TAGGING",49),{error:4};
if(b.version!==1)return db("TAGGING",50),{error:5};try{var e=Qr(b);a&&e&&Or({value:b,error:0})}catch(f){return db("TAGGING",48),{error:8}}return{value:b,error:0}}
function Qr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,db("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Qr(a[e.value])||c;return c}return!1}
function Or(a){if(a.error)return a.error;if(!a.value)return db("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return db("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return db("TAGGING",53),7}return 0};var Rr={wj:"value",Hb:"conversionCount"},Sr=[Rr,{lm:10,Dm:11,wj:"timeouts",Hb:"timeouts"}];function Tr(){var a=Rr;if(!Ur(a))return{};var b=Vr(Sr),c=b[a.Hb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Hb]=c+1,d));return Wr(e)?e:b}
function Vr(a){var b;a:{var c=Pr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Ur(m)){var n=e[m.wj];n===void 0||Number.isNaN(n)?f[m.Hb]=-1:f[m.Hb]=Number(n)}else f[m.Hb]=-1}return f}
function Wr(a,b){b=b||{};for(var c=zb(),d=Kr(b,c,!0),e={},f=l(Sr),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Hb];m!==void 0&&m!==-1&&(e[h.wj]=m)}e.creationTimeMs=c;return Mr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Ur(a){return Um(["ad_storage","ad_user_data"])?!a.Dm||Ja(a.Dm):!1}function Xr(a){return Um(["ad_storage","ad_user_data"])?!a.lm||Ja(a.lm):!1};function Yr(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Zr={O:{yo:0,Tj:1,wg:2,Zj:3,Nh:4,Xj:5,Yj:6,bk:7,Oh:8,jl:9,il:10,zi:11,kl:12,ah:13,ol:14,Mf:15,vo:16,te:17,Si:18,Ti:19,Ui:20,Rl:21,Vi:22,Qh:23,jk:24}};Zr.O[Zr.O.yo]="RESERVED_ZERO";Zr.O[Zr.O.Tj]="ADS_CONVERSION_HIT";Zr.O[Zr.O.wg]="CONTAINER_EXECUTE_START";Zr.O[Zr.O.Zj]="CONTAINER_SETUP_END";Zr.O[Zr.O.Nh]="CONTAINER_SETUP_START";Zr.O[Zr.O.Xj]="CONTAINER_BLOCKING_END";Zr.O[Zr.O.Yj]="CONTAINER_EXECUTE_END";Zr.O[Zr.O.bk]="CONTAINER_YIELD_END";Zr.O[Zr.O.Oh]="CONTAINER_YIELD_START";Zr.O[Zr.O.jl]="EVENT_EXECUTE_END";
Zr.O[Zr.O.il]="EVENT_EVALUATION_END";Zr.O[Zr.O.zi]="EVENT_EVALUATION_START";Zr.O[Zr.O.kl]="EVENT_SETUP_END";Zr.O[Zr.O.ah]="EVENT_SETUP_START";Zr.O[Zr.O.ol]="GA4_CONVERSION_HIT";Zr.O[Zr.O.Mf]="PAGE_LOAD";Zr.O[Zr.O.vo]="PAGEVIEW";Zr.O[Zr.O.te]="SNIPPET_LOAD";Zr.O[Zr.O.Si]="TAG_CALLBACK_ERROR";Zr.O[Zr.O.Ti]="TAG_CALLBACK_FAILURE";Zr.O[Zr.O.Ui]="TAG_CALLBACK_SUCCESS";Zr.O[Zr.O.Rl]="TAG_EXECUTE_END";Zr.O[Zr.O.Vi]="TAG_EXECUTE_START";Zr.O[Zr.O.Qh]="CUSTOM_PERFORMANCE_START";Zr.O[Zr.O.jk]="CUSTOM_PERFORMANCE_END";var $r=[],as={},bs={};var cs=["1"];function ds(a){return a.origin!=="null"};function es(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ja(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function fs(a,b,c,d){if(!gs(d))return[];if($r.includes("1")){var e;(e=Yc())==null||e.mark("1-"+Zr.O.Qh+"-"+(bs["1"]||0))}var f=es(a,String(b||hs()),c);if($r.includes("1")){var g="1-"+Zr.O.jk+"-"+(bs["1"]||0),h={start:"1-"+Zr.O.Qh+"-"+(bs["1"]||0),end:g},m;(m=Yc())==null||m.mark(g);var n,p,q=(p=(n=Yc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(bs["1"]=(bs["1"]||0)+1,as["1"]=q+(as["1"]||0))}return f}
function is(a,b,c,d,e){if(gs(e)){var f=js(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=ks(f,function(g){return g.bp},b);if(f.length===1)return f[0];f=ks(f,function(g){return g.fq},c);return f[0]}}}function ls(a,b,c,d){var e=hs(),f=window;ds(f)&&(f.document.cookie=a);var g=hs();return e!==g||c!==void 0&&fs(b,g,!1,d).indexOf(c)>=0}
function ms(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!gs(c.Fc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ns(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Zp);g=e(g,"samesite",c.sq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=os(),q=void 0,r=!1,t=0;t<p.length;++t){var v=p[t]!=="none"?p[t]:void 0,u=e(g,"domain",v);u=f(u,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!ps(v,c.path)&&ls(u,a,b,c.Fc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return ps(n,c.path)?1:ls(g,a,b,c.Fc)?0:1}function qs(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return ms(a,b,c)}
function ks(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function js(a,b,c){for(var d=[],e=fs(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({To:e[f],Uo:g.join("."),bp:Number(n[0])||1,fq:Number(n[1])||1})}}}return d}function ns(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var rs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,ss=/(^|\.)doubleclick\.net$/i;function ps(a,b){return a!==void 0&&(ss.test(window.document.location.hostname)||b==="/"&&rs.test(a))}function ts(a){if(!a)return 1;var b=a;Ja(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function us(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function vs(a,b){var c=""+ts(a),d=us(b);d>1&&(c+="-"+d);return c}
var hs=function(){return ds(window)?window.document.cookie:""},gs=function(a){return a&&Ja(8)?(Array.isArray(a)?a:[a]).every(function(b){return Wm(b)&&Um(b)}):!0},os=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;ss.test(e)||rs.test(e)||a.push("none");return a};function ws(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Yr(a)&2147483647):String(b)}function xs(a){return[ws(a),Math.round(zb()/1E3)].join(".")}function ys(a,b,c,d,e){var f=ts(b),g;return(g=is(a,f,us(c),d,e))==null?void 0:g.Uo};var zs;function As(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Bs,d=Cs,e=Ds();if(!e.init){Hc(z,"mousedown",a);Hc(z,"keyup",a);Hc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Es(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ds().decorators.push(f)}
function Fs(a,b,c){for(var d=Ds().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Cb(e,g.callback())}}return e}
function Ds(){var a=uc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Gs=/(.*?)\*(.*?)\*(.*)/,Hs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Is=/^(?:www\.|m\.|amp\.)+/,Js=/([^?#]+)(\?[^#]*)?(#.*)?/;function Ks(a){var b=Js.exec(a);if(b)return{Cj:b[1],query:b[2],fragment:b[3]}}function Ls(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Ms(a,b){var c=[qc.userAgent,(new Date).getTimezoneOffset(),qc.userLanguage||qc.language,Math.floor(zb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=zs)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}zs=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^zs[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ns(a){return function(b){var c=Pk(x.location.href),d=c.search.replace("?",""),e=Gk(d,"_gl",!1,!0)||"";b.query=Os(e)||{};var f=Jk(c,"fragment"),g;var h=-1;if(Eb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Os(g||"")||{};a&&Ps(c,d,f)}}function Qs(a,b){var c=Ls(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ps(a,b,c){function d(g,h){var m=Qs("_gl",g);m.length&&(m=h+m);return m}if(pc&&pc.replaceState){var e=Ls("_gl");if(e.test(b)||e.test(c)){var f=Jk(a,"path");b=d(b,"?");c=d(c,"#");pc.replaceState({},"",""+f+b+c)}}}function Rs(a,b){var c=Ns(!!b),d=Ds();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Cb(e,f.query),a&&Cb(e,f.fragment));return e}
var Os=function(a){try{var b=Ss(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=bb(d[e+1]);c[f]=g}db("TAGGING",6);return c}}catch(h){db("TAGGING",8)}};function Ss(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Gs.exec(d);if(f){c=f;break a}d=Ik(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Ms(h,p)){m=!0;break a}m=!1}if(m)return h;db("TAGGING",7)}}}
function Ts(a,b,c,d,e){function f(p){p=Qs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Ks(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Cj+h+m}
function Us(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var v,u=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(u.push(w),u.push(ab(String(y))))}var A=u.join("*");v=["1",Ms(A),A].join("*");d?(Ja(3)||Ja(1)||!p)&&Vs("_gl",v,a,p,q):Ws("_gl",v,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Fs(b,1,d),f=Fs(b,2,d),g=Fs(b,4,d),h=Fs(b,3,d);c(e,!1,!1);c(f,!0,!1);Ja(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Xs(m,h[m],a)}function Xs(a,b,c){c.tagName.toLowerCase()==="a"?Ws(a,b,c):c.tagName.toLowerCase()==="form"&&Vs(a,b,c)}function Ws(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ja(5)||d)){var h=x.location.href,m=Ks(c.href),n=Ks(h);g=!(m&&n&&m.Cj===n.Cj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Ts(a,b,c.href,d,e);ec.test(p)&&(c.href=p)}}
function Vs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Ts(a,b,f,d,e);ec.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Bs(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Us(e,e.hostname)}}catch(g){}}function Cs(a){try{var b=a.getAttribute("action");if(b){var c=Jk(Pk(b),"host");Us(a,c)}}catch(d){}}function Ys(a,b,c,d){As();var e=c==="fragment"?2:1;d=!!d;Es(a,b,e,d,!1);e===2&&db("TAGGING",23);d&&db("TAGGING",24)}
function Zs(a,b){As();Es(a,[Lk(x.location,"host",!0)],b,!0,!0)}function $s(){var a=z.location.hostname,b=Hs.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Ik(f[2])||"":Ik(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Is,""),m=e.replace(Is,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function at(a,b){return a===!1?!1:a||b||$s()};var bt=["1"],ct={},dt={};function et(a,b){b=b===void 0?!0:b;var c=ft(a.prefix);if(ct[c])gt(a);else if(ht(c,a.path,a.domain)){var d=dt[ft(a.prefix)]||{id:void 0,Fh:void 0};b&&it(a,d.id,d.Fh);gt(a)}else{var e=Rk("auiddc");if(e)db("TAGGING",17),ct[c]=e;else if(b){var f=ft(a.prefix),g=xs();jt(f,g,a);ht(c,a.path,a.domain);gt(a,!0)}}}
function gt(a,b){if((b===void 0?0:b)&&Ur(Rr)){var c=Nr(!1);c.error!==0?db("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Or(c)!==0&&db("TAGGING",41)):db("TAGGING",40):db("TAGGING",39)}if(Xr(Rr)&&Vr([Rr])[Rr.Hb]===-1){for(var d={},e=(d[Rr.Hb]=0,d),f=l(Sr),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Rr&&Xr(h)&&(e[h.Hb]=0)}Wr(e,a)}}
function it(a,b,c){var d=ft(a.prefix),e=ct[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(zb()/1E3)));jt(d,h,a,g*1E3)}}}}function jt(a,b,c,d){var e;e=["1",vs(c.domain,c.path),b].join(".");var f=Kr(c,d);f.Fc=kt();qs(a,e,f)}function ht(a,b,c){var d=ys(a,b,c,bt,kt());if(!d)return!1;lt(a,d);return!0}
function lt(a,b){var c=b.split(".");c.length===5?(ct[a]=c.slice(0,2).join("."),dt[a]={id:c.slice(2,4).join("."),Fh:Number(c[4])||0}):c.length===3?dt[a]={id:c.slice(0,2).join("."),Fh:Number(c[2])||0}:ct[a]=b}function ft(a){return(a||"_gcl")+"_au"}function mt(a){function b(){Um(c)&&a()}var c=kt();$m(function(){b();Um(c)||an(b,c)},c)}
function nt(a){var b=Rs(!0),c=ft(a.prefix);mt(function(){var d=b[c];if(d){lt(c,d);var e=Number(ct[c].split(".")[1])*1E3;if(e){db("TAGGING",16);var f=Kr(a,e);f.Fc=kt();var g=["1",vs(a.domain,a.path),d].join(".");qs(c,g,f)}}})}function ot(a,b,c,d,e){e=e||{};var f=function(){var g={},h=ys(a,e.path,e.domain,bt,kt());h&&(g[a]=h);return g};mt(function(){Ys(f,b,c,d)})}function kt(){return["ad_storage","ad_user_data"]};function pt(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Pj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function qt(a,b){var c=pt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Pj]||(d[c[e].Pj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Pj].push(g)}}return d};var rt={},st=(rt.k={da:/^[\w-]+$/},rt.b={da:/^[\w-]+$/,Jj:!0},rt.i={da:/^[1-9]\d*$/},rt.h={da:/^\d+$/},rt.t={da:/^[1-9]\d*$/},rt.d={da:/^[A-Za-z0-9_-]+$/},rt.j={da:/^\d+$/},rt.u={da:/^[1-9]\d*$/},rt.l={da:/^[01]$/},rt.o={da:/^[1-9]\d*$/},rt.g={da:/^[01]$/},rt.s={da:/^.+$/},rt);var tt={},xt=(tt[5]={Kh:{2:ut},vj:"2",uh:["k","i","b","u"]},tt[4]={Kh:{2:ut,GCL:vt},vj:"2",uh:["k","i","b"]},tt[2]={Kh:{GS2:ut,GS1:wt},vj:"GS2",uh:"sogtjlhd".split("")},tt);function zt(a,b,c){var d=xt[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kh[e];if(f)return f(a,b)}}}
function ut(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=xt[b];if(f){for(var g=f.uh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=st[p];r&&(r.Jj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function At(a,b,c){var d=xt[b];if(d)return[d.vj,c||"1",Bt(a,b)].join(".")}
function Bt(a,b){var c=xt[b];if(c){for(var d=[],e=l(c.uh),f=e.next();!f.done;f=e.next()){var g=f.value,h=st[g];if(h){var m=a[g];if(m!==void 0)if(h.Jj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function vt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function wt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Ct=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Dt(a,b,c){if(xt[b]){for(var d=[],e=fs(a,void 0,void 0,Ct.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=zt(g.value,b,c);h&&d.push(Et(h))}return d}}function Ft(a,b,c,d,e){d=d||{};var f=vs(d.domain,d.path),g=At(b,c,f);if(!g)return 1;var h=Kr(d,e,void 0,Ct.get(c));return qs(a,g,h)}function Gt(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Et(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=st[e];d.Uf?d.Uf.Jj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Gt(h,g.Uf)}}(d)):void 0:typeof f==="string"&&Gt(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Ht=function(){this.value=0};Ht.prototype.set=function(a){return this.value|=1<<a};var It=function(a,b){b<=0||(a.value|=1<<b-1)};Ht.prototype.get=function(){return this.value};Ht.prototype.clear=function(a){this.value&=~(1<<a)};Ht.prototype.clearAll=function(){this.value=0};Ht.prototype.equals=function(a){return this.value===a.value};function Jt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Kt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]}
function Lt(a){if(!a||a.length<50||a.length>200)return!1;var b=Jt(a),c;if(b)a:{if(b&&b.length!==0){var d=0;try{for(;d<b.length;){var e=Kt(b,d);if(e===void 0)break;var f=l(e),g=f.next().value,h=f.next().value,m=g,n=h,p=m&7;if(m>>3===16382){if(p!==0)break;var q=Kt(b,n);if(q===void 0)break;c=l(q).next().value===1;break a}var r;b:{var t=void 0;switch(p){case 0:r=(t=Kt(b,n))==null?void 0:t[1];break b;case 1:r=n+8;break b;case 2:var v=Kt(b,n);if(v===void 0)break;var u=l(v),w=u.next().value;r=u.next().value+
w;break b;case 5:r=n+4;break b}r=void 0}var y=r;if(y===void 0||y>b.length)break;d=y}}catch(A){}}c=!1}else c=!1;return c};function Mt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Mb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Mb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Yr((""+b+e).toLowerCase()))};var Nt={},Ot=(Nt.gclid=!0,Nt.dclid=!0,Nt.gbraid=!0,Nt.wbraid=!0,Nt),Pt=/^\w+$/,Qt=/^[\w-]+$/,Rt={},St=(Rt.aw="_aw",Rt.dc="_dc",Rt.gf="_gf",Rt.gp="_gp",Rt.gs="_gs",Rt.ha="_ha",Rt.ag="_ag",Rt.gb="_gb",Rt),Tt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Ut=/^www\.googleadservices\.com$/;function Vt(){return["ad_storage","ad_user_data"]}function Wt(a){return!Ja(8)||Um(a)}function Xt(a,b){function c(){var d=Wt(b);d&&a();return d}$m(function(){c()||an(c,b)},b)}
function Yt(a){return Zt(a).map(function(b){return b.gclid})}function $t(a){return au(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function au(a){var b=bu(a.prefix),c=cu("gb",b),d=cu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=Zt(c).map(e("gb")),g=du(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function eu(a,b,c,d,e,f){var g=ob(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Gd=f),g.labels=fu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Gd:f})}function du(a){for(var b=Dt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=gu(f);if(n){var p=void 0;Ja(9)&&(p=f.u);eu(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}
function Zt(a){for(var b=[],c=fs(a,z.cookie,void 0,Vt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=hu(e.value);if(f!=null){var g=f;eu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return iu(b)}function ju(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function ku(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Ht,q=(n=b.Ka)!=null?n:new Ht;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Gd=b.Gd);d.labels=ju(d.labels||[],b.labels||[]);d.Bb=ju(d.Bb||[],b.Bb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function lu(a){if(!a)return new Ht;var b=new Ht;if(a===1)return It(b,2),It(b,3),b;It(b,a);return b}
function mu(){var a=Pr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Qt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Ht;typeof e==="number"?g=lu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Bb:[2]}}catch(h){return null}}
function nu(){var a=Pr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Qt))return b;var f=new Ht,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Bb:[2]});return b},[])}catch(b){return null}}
function ou(a){for(var b=[],c=fs(a,z.cookie,void 0,Vt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=hu(e.value);f!=null&&(f.Gd=void 0,f.Ka=new Ht,f.Bb=[1],ku(b,f))}var g=mu();g&&(g.Gd=void 0,g.Bb=g.Bb||[2],ku(b,g));if(Ja(14)){var h=nu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Gd=void 0;p.Bb=p.Bb||[2];ku(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return iu(b)}
function fu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function bu(a){return a&&typeof a==="string"&&a.match(Pt)?a:"_gcl"}function pu(a,b){if(a){var c={value:a,Ka:new Ht};It(c.Ka,b);return c}}
function qu(a,b,c,d){var e=Pk(a),f=Jk(e,"query",!1,void 0,"gclsrc"),g=pu(Jk(e,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!g||!f)){var h=e.hash.replace("#","");g||(g=pu(Gk(h,"gclid",!1),3));f||(f=Gk(h,"gclsrc",!1))}var m;if(d&&!Lt((m=g)==null?void 0:m.value)){var n;a:{for(var p=Hk(Jk(e,"query")),q=l(Object.keys(p)),r=q.next();!r.done;r=q.next()){var t=r.value;if(!Ot[t]){var v=p[t][0]||"";if(Lt(v)){n=v;break a}}}n=void 0}var u=n,w;u&&u!==((w=g)==null?void 0:w.value)&&(g=pu(u,7))}return!g||f!==void 0&&
f!=="aw"&&f!=="aw.ds"?[]:[g]}function ru(a,b){var c=Pk(a),d=Jk(c,"query",!1,void 0,"gclid"),e=Jk(c,"query",!1,void 0,"gclsrc"),f=Jk(c,"query",!1,void 0,"wbraid");f=Kb(f);var g=Jk(c,"query",!1,void 0,"gbraid"),h=Jk(c,"query",!1,void 0,"gad_source"),m=Jk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Gk(n,"gclid",!1);e=e||Gk(n,"gclsrc",!1);f=f||Gk(n,"wbraid",!1);g=g||Gk(n,"gbraid",!1);h=h||Gk(n,"gad_source",!1)}return su(d,e,m,f,g,h)}
function tu(){return ru(x.location.href,!0)}
function su(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Qt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Qt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Qt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Qt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function uu(a){for(var b=tu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=ru(x.document.referrer,!1),b.gad_source=void 0);vu(b,!1,a)}
function wu(a){uu(a);var b=qu(x.location.href,!0,!1,Ja(15)?xu(yu()):!1);b.length||(b=qu(x.document.referrer,!1,!0,!1));if(b.length){var c=b[0];a=a||{};var d=zb(),e=Kr(a,d,!0),f=Vt(),g=function(){Wt(f)&&e.expires!==void 0&&Mr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};$m(function(){g();Wt(f)||an(g,f)},f)}}
function zu(a,b,c){c=c||{};var d=zb(),e=Kr(c,d,!0),f=Vt(),g=function(){if(Wt(f)&&e.expires!==void 0){var h=nu()||[];ku(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:lu(b)},!0);Mr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};$m(function(){Wt(f)?g():an(g,f)},f)}
function vu(a,b,c,d,e){c=c||{};e=e||[];var f=bu(c.prefix),g=d||zb(),h=Math.round(g/1E3),m=Vt(),n=!1,p=!1,q=function(){if(Wt(m)){var r=Kr(c,g,!0);r.Fc=m;for(var t=function(Q,ca){var U=cu(Q,f);U&&(qs(U,ca,r),Q!=="gb"&&(n=!0))},v=function(Q){var ca=["GCL",h,Q];e.length>0&&ca.push(e.join("."));return ca.join(".")},u=l(["aw","dc","gf","ha","gp"]),w=u.next();!w.done;w=u.next()){var y=w.value;a[y]&&t(y,v(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],C=cu("gb",f);!b&&Zt(C).some(function(Q){return Q.gclid===A&&Q.labels&&
Q.labels.length>0})||t("gb",v(A))}}if(!p&&a.gbraid&&Wt("ad_storage")&&(p=!0,!n)){var D=a.gbraid,F=cu("ag",f);if(b||!du(F).some(function(Q){return Q.gclid===D&&Q.labels&&Q.labels.length>0})){var H={},L=(H.k=D,H.i=""+h,H.b=e,H);Ft(F,L,5,c,g)}}Au(a,f,g,c)};$m(function(){q();Wt(m)||an(q,m)},m)}
function Au(a,b,c,d){if(a.gad_source!==void 0&&Wt("ad_storage")){if(Ja(4)){var e=Xc();if(e==="r"||e==="h")return}var f=a.gad_source,g=cu("gs",b);if(g){var h=Math.floor((zb()-(Wc()||0))/1E3),m;if(Ja(9)){var n=Mt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Ft(g,m,5,d,c)}}}
function Bu(a,b){var c=Rs(!0);Xt(function(){for(var d=bu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(St[f]!==void 0){var g=cu(f,d),h=c[g];if(h){var m=Math.min(Cu(h),zb()),n;b:{for(var p=m,q=fs(g,z.cookie,void 0,Vt()),r=0;r<q.length;++r)if(Cu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Kr(b,m,!0);t.Fc=Vt();qs(g,h,t)}}}}vu(su(c.gclid,c.gclsrc),!1,b)},Vt())}
function Du(a){var b=["ag"],c=Rs(!0),d=bu(a.prefix);Xt(function(){for(var e=0;e<b.length;++e){var f=cu(b[e],d);if(f){var g=c[f];if(g){var h=zt(g,5);if(h){var m=gu(h);m||(m=zb());var n;a:{for(var p=m,q=Dt(f,5),r=0;r<q.length;++r)if(gu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ft(f,h,5,a,m)}}}}},["ad_storage"])}function cu(a,b){var c=St[a];if(c!==void 0)return b+c}function Cu(a){return Eu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function gu(a){return a?(Number(a.i)||0)*1E3:0}function hu(a){var b=Eu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Eu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Qt.test(a[2])?[]:a}
function Fu(a,b,c,d,e){if(Array.isArray(b)&&ds(x)){var f=bu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=cu(a[m],f);if(n){var p=fs(n,z.cookie,void 0,Vt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Xt(function(){Ys(g,b,c,d)},Vt())}}
function Gu(a,b,c,d){if(Array.isArray(a)&&ds(x)){var e=["ag"],f=bu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=cu(e[m],f);if(!n)return{};var p=Dt(n,5);if(p.length){var q=p.sort(function(r,t){return gu(t)-gu(r)})[0];h[n]=At(q,5)}}return h};Xt(function(){Ys(g,a,b,c)},["ad_storage"])}}function iu(a){return a.filter(function(b){return Qt.test(b.gclid)})}
function Hu(a,b){if(ds(x)){for(var c=bu(b.prefix),d={},e=0;e<a.length;e++)St[a[e]]&&(d[a[e]]=St[a[e]]);Xt(function(){sb(d,function(f,g){var h=fs(c+g,z.cookie,void 0,Vt());h.sort(function(t,v){return Cu(v)-Cu(t)});if(h.length){var m=h[0],n=Cu(m),p=Eu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Eu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];vu(q,!0,b,n,p)}})},Vt())}}
function Iu(a){var b=["ag"],c=["gbraid"];Xt(function(){for(var d=bu(a.prefix),e=0;e<b.length;++e){var f=cu(b[e],d);if(!f)break;var g=Dt(f,5);if(g.length){var h=g.sort(function(q,r){return gu(r)-gu(q)})[0],m=gu(h),n=h.b,p={};p[c[e]]=h.k;vu(p,!0,a,m,n)}}},["ad_storage"])}function Ju(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Ku(a){function b(h,m,n){n&&(h[m]=n)}if(Xm()){var c=tu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Rs(!1)._gs);if(Ju(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);Zs(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);Zs(function(){return g},1)}}}
function Lu(a){if(!Ja(1))return null;var b=Rs(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ja(2)){var c=Pk(x.location.href);b=Jk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=tu();if(Ju(d,a))return"0"}return null}function Mu(a){var b=Lu(a);b!=null&&Zs(function(){var c={};return c.gad_source=b,c},4)}
function Nu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Ou(a,b,c,d){var e=[];c=c||{};if(!Wt(Vt()))return e;var f=Zt(a),g=Nu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Kr(c,p,!0);r.Fc=Vt();qs(a,q,r)}return e}
function Pu(a,b){var c=[];b=b||{};var d=au(b),e=Nu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=bu(b.prefix),n=cu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,v=p.timestamp,u=Math.round(v/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+u,w.b=(t||[]).concat([a]),w);Ft(n,y,5,b,v)}else if(h.type==="gb"){var A=[q,u,r].concat(t||[],[a]).join("."),C=Kr(b,v,!0);C.Fc=Vt();qs(n,A,C)}}return c}
function Qu(a,b){var c=bu(b),d=cu(a,c);if(!d)return 0;var e;e=a==="ag"?du(d):Zt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Ru(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Su(a){var b=Math.max(Qu("aw",a),Ru(Wt(Vt())?qt():{})),c=Math.max(Qu("gb",a),Ru(Wt(Vt())?qt("_gac_gb",!0):{}));c=Math.max(c,Qu("ag",a));return c>b}
function xu(a){return Tt.test(a)||Ut.test(a)}function yu(){return z.referrer?Jk(Pk(z.referrer),"host"):""};
var Tu=function(a,b){b=b===void 0?!1:b;var c=sp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},Uu=function(a){return Qk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},av=function(a,b,c,d,e){var f=bu(a.prefix);if(Tu(f,!0)){var g=tu(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=Vu(),r=q.Zf,t=q.im;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Cd:p});n&&h.push({gclid:n,Cd:"ds"});h.length===2&&M(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Cd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Cd:"aw.ds"});Wu(function(){var v=hp(Xu());if(v){et(a);var u=[],w=v?ct[ft(a.prefix)]:void 0;w&&u.push("auid="+w);if(hp(J.m.V)){e&&u.push("userId="+e);var y=qn(mn.Z.Ll);if(y===void 0)pn(mn.Z.Ml,!0);else{var A=qn(mn.Z.ph);u.push("ga_uid="+A+"."+y)}}var C=yu(),D=v||!d?h:[];D.length===0&&xu(C)&&D.push({gclid:"",Cd:""});if(D.length!==0||r!==void 0){C&&u.push("ref="+encodeURIComponent(C));var F=Yu();u.push("url="+encodeURIComponent(F));
u.push("tft="+zb());var H=Wc();H!==void 0&&u.push("tfd="+Math.round(H));var L=El(!0);u.push("frm="+L);r!==void 0&&u.push("gad_source="+encodeURIComponent(r));t!==void 0&&u.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var Q={};c=fq(Wp(new Vp(0),(Q[J.m.Ea]=Cq.C[J.m.Ea],Q)))}u.push("gtm="+Ir({Ma:b}));tr()&&u.push("gcs="+ur());u.push("gcd="+yr(c));Br()&&u.push("dma_cps="+zr());u.push("dma="+Ar());sr(c)?u.push("npa=0"):u.push("npa=1");Dr()&&u.push("_ng=1");Xq(er())&&u.push("tcfd="+Cr());
var ca=lr();ca&&u.push("gdpr="+ca);var U=kr();U&&u.push("gdpr_consent="+U);E(23)&&u.push("apve=0");E(123)&&Rs(!1)._up&&u.push("gtm_up=1");hk()&&u.push("tag_exp="+hk());if(D.length>0)for(var qa=0;qa<D.length;qa++){var T=D[qa],Z=T.gclid,Y=T.Cd;if(!Zu(a.prefix,Y+"."+Z,w!==void 0)){var V=$u+"?"+u.join("&");Z!==""?V=Y==="gb"?V+"&wbraid="+Z:V+"&gclid="+Z+"&gclsrc="+Y:Y==="aw.ds"&&(V+="&gclsrc=aw.ds");Oc(V)}}else if(r!==void 0&&!Zu(a.prefix,"gad",w!==void 0)){var ka=$u+"?"+u.join("&");Oc(ka)}}}})}},Zu=function(a,
b,c){var d=sp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},Vu=function(){var a=Pk(x.location.href),b=void 0,c=void 0,d=Jk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(bv);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Zf:b,im:c}},Yu=function(){var a=El(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},cv=function(a){var b=[];sb(a,function(c,d){d=iu(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);
e.length&&b.push(c+":"+e.join(","))});return b.join(";")},ev=function(a,b){return dv("dc",a,b)},fv=function(a,b){return dv("aw",a,b)},dv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Rk("gcl"+a);if(d)return d.split(".")}var e=bu(b);if(e==="_gcl"){var f=!hp(Xu())&&c,g;g=tu()[a]||[];if(g.length>0)return f?["0"]:g}var h=cu(a,e);return h?Yt(h):[]},Wu=function(a){var b=Xu();kp(function(){a();hp(b)||an(a,b)},b)},Xu=function(){return[J.m.U,J.m.V]},$u=Ni(36,'https://adservice.google.com/pagead/regclk'),
bv=/^gad_source[_=](\d+)$/;function gv(){return sp("dedupe_gclid",function(){return xs()})};var hv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,iv=/^www.googleadservices.com$/;function jv(a){a||(a=kv());return a.Jq?!1:a.Hp||a.Ip||a.Lp||a.Jp||a.Zf||a.rp||a.Kp||a.xp?!0:!1}function kv(){var a={},b=Rs(!0);a.Jq=!!b._up;var c=tu();a.Hp=c.aw!==void 0;a.Ip=c.dc!==void 0;a.Lp=c.wbraid!==void 0;a.Jp=c.gbraid!==void 0;a.Kp=c.gclsrc==="aw.ds";a.Zf=Vu().Zf;var d=z.referrer?Jk(Pk(z.referrer),"host"):"";a.xp=hv.test(d);a.rp=iv.test(d);return a};function lv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function mv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function nv(){return["ad_storage","ad_user_data"]}function ov(a){if(E(38)&&!qn(mn.Z.Al)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{lv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(pn(mn.Z.Al,function(d){d.gclid&&zu(d.gclid,5,a)}),mv(c)||M(178))})}catch(c){M(177)}};$m(function(){Wt(nv())?b():an(b,nv())},nv())}};var pv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function qv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?pn(mn.Z.Of,{gadSource:a.data.gadSource}):M(173)}
function rv(a,b){if(E(a)){if(qn(mn.Z.Of))return M(176),mn.Z.Of;if(qn(mn.Z.Cl))return M(170),mn.Z.Of;var c=Gl();if(!c)M(171);else if(c.opener){var d=function(g){if(pv.includes(g.origin)){a===119?qv(g):a===200&&(qv(g),g.data.gclid&&zu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Pq(c,"message",d)}else M(172)};if(Oq(c,"message",d)){pn(mn.Z.Cl,!0);for(var e=l(pv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);M(174);return mn.Z.Of}M(175)}}}
;var sv=function(){this.C=this.gppString=void 0};sv.prototype.reset=function(){this.C=this.gppString=void 0};var tv=new sv;var uv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),vv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,wv=/^\d+\.fls\.doubleclick\.net$/,xv=/;gac=([^;?]+)/,yv=/;gacgb=([^;?]+)/;
function zv(a,b){if(wv.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(uv)?Ik(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Av(a,b,c){for(var d=Wt(Vt())?qt("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Ou("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{qp:f?e.join(";"):"",pp:zv(d,yv)}}function Bv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(vv)?b[1]:void 0}
function Cv(a){var b=Ja(9),c={},d,e,f;wv.test(z.location.host)&&(d=Bv("gclgs"),e=Bv("gclst"),b&&(f=Bv("gcllp")));if(d&&e&&(!b||f))c.yh=d,c.Ah=e,c.zh=f;else{var g=zb(),h=du((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Gd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.yh=m.join("."),c.Ah=n.join("."),b&&p.length>0&&(c.zh=p.join(".")))}return c}
function Dv(a,b,c,d){d=d===void 0?!1:d;if(wv.test(z.location.host)){var e=Bv(c);if(e){if(d){var f=new Ht;It(f,2);It(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Bb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?ou(g):Zt(g)}if(b==="wbraid")return Zt((a||"_gcl")+"_gb");if(b==="braids")return au({prefix:a})}return[]}function Ev(a){return wv.test(z.location.host)?!(Bv("gclaw")||Bv("gac")):Su(a)}
function Fv(a,b,c){var d;d=c?Pu(a,b):Ou((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Gv(){var a=x.__uspapi;if(jb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Lv=function(a){if(a.eventName===J.m.qa&&P(a,O.A.fa)===K.J.Ga)if(E(24)){R(a,O.A.se,N(a.D,J.m.za)!=null&&N(a.D,J.m.za)!==!1&&!hp([J.m.U,J.m.V]));var b=Hv(a),c=N(a.D,J.m.Oa)!==!1;c||S(a,J.m.Vh,"1");var d=bu(b.prefix),e=P(a,O.A.jh);if(!P(a,O.A.ja)&&!P(a,O.A.Rf)&&!P(a,O.A.qe)){var f=N(a.D,J.m.Eb),g=N(a.D,J.m.Pa)||{};Iv({xe:c,De:g,Ge:f,Tc:b});if(!e&&!Tu(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{S(a,J.m.kd,J.m.bd);if(P(a,O.A.ja))S(a,J.m.kd,J.m.pn),S(a,J.m.ja,"1");else if(P(a,O.A.Rf))S(a,J.m.kd,
J.m.zn);else if(P(a,O.A.qe))S(a,J.m.kd,J.m.wn);else{var h=tu();S(a,J.m.dd,h.gclid);S(a,J.m.hd,h.dclid);S(a,J.m.rk,h.gclsrc);Jv(a,J.m.dd)||Jv(a,J.m.hd)||(S(a,J.m.Xd,h.wbraid),S(a,J.m.Pe,h.gbraid));S(a,J.m.Va,yu());S(a,J.m.Aa,Yu());if(E(27)&&tc){var m=Jk(Pk(tc),"host");m&&S(a,J.m.Yk,m)}if(!P(a,O.A.qe)){var n=Vu(),p=n.im;S(a,J.m.Ne,n.Zf);S(a,J.m.Oe,p)}S(a,J.m.Lc,El(!0));var q=kv();jv(q)&&S(a,J.m.md,"1");S(a,J.m.tk,gv());Rs(!1)._up==="1"&&S(a,J.m.Ok,"1")}Qn=!0;S(a,J.m.Db);S(a,J.m.Qb);var r=hp([J.m.U,
J.m.V]);r&&(S(a,J.m.Db,Kv()),c&&(et(b),S(a,J.m.Qb,ct[ft(b.prefix)])));S(a,J.m.oc);S(a,J.m.mb);if(!Jv(a,J.m.dd)&&!Jv(a,J.m.hd)&&Ev(d)){var t=$t(b);t.length>0&&S(a,J.m.oc,t.join("."))}else if(!Jv(a,J.m.Xd)&&r){var v=Yt(d+"_aw");v.length>0&&S(a,J.m.mb,v.join("."))}E(31)&&S(a,J.m.Rk,Xc());a.D.isGtmEvent&&(a.D.C[J.m.Ea]=Cq.C[J.m.Ea]);sr(a.D)?S(a,J.m.zc,!1):S(a,J.m.zc,!0);R(a,O.A.ug,!0);var u=Gv();u!==void 0&&S(a,J.m.Cf,u||"error");var w=lr();w&&S(a,J.m.ld,w);if(E(137))try{var y=Intl.DateTimeFormat().resolvedOptions().timeZone;
S(a,J.m.ni,y||"-")}catch(F){S(a,J.m.ni,"e")}var A=kr();A&&S(a,J.m.pd,A);var C=tv.gppString;C&&S(a,J.m.hf,C);var D=tv.C;D&&S(a,J.m.ff,D);R(a,O.A.Ha,!1)}}else a.isAborted=!0},Hv=function(a){var b={prefix:N(a.D,J.m.Sb)||N(a.D,J.m.eb),domain:N(a.D,J.m.ob),Dc:N(a.D,J.m.pb),flags:N(a.D,J.m.yb)};a.D.isGtmEvent&&(b.path=N(a.D,J.m.Tb));return b},Mv=function(a,b){var c,d,e,f,g,h,m,n;c=a.xe;d=a.De;e=a.Ge;f=a.Ma;g=a.D;h=a.Ee;m=a.Er;n=a.Rm;Iv({xe:c,De:d,Ge:e,Tc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,av(b,
f,g,h,n))},Nv=function(a,b){if(!P(a,O.A.qe)){var c=rv(119);if(c){var d=qn(c),e=function(g){R(a,O.A.qe,!0);var h=Jv(a,J.m.Ne),m=Jv(a,J.m.Oe);S(a,J.m.Ne,String(g.gadSource));S(a,J.m.Oe,6);R(a,O.A.ja);R(a,O.A.Rf);S(a,J.m.ja);b();S(a,J.m.Ne,h);S(a,J.m.Oe,m);R(a,O.A.qe,!1)};if(d)e(d);else{var f=void 0;f=sn(c,function(g,h){e(h);tn(c,f)})}}}},Iv=function(a){var b,c,d,e;b=a.xe;c=a.De;d=a.Ge;e=a.Tc;b&&(at(c[J.m.de],!!c[J.m.ma])&&(Bu(Ov,e),Du(e),nt(e)),El()!==2?(wu(e),ov(e),rv(200,e)):uu(e),Hu(Ov,e),Iu(e));
c[J.m.ma]&&(Fu(Ov,c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e.prefix),Gu(c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e.prefix),ot(ft(e.prefix),c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e),ot("FPAU",c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e));d&&(E(101)?Ku(Pv):Ku(Qv));Mu(Qv)},Rv=function(a,b,c,d){var e,f,g;e=a.Sm;f=a.callback;g=a.rm;if(typeof f==="function")if(e===J.m.mb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===J.m.Qb?(M(65),et(b,!1),f(ct[ft(b.prefix)])):f(g)},Sv=function(a,b){Array.isArray(b)||
(b=[b]);var c=P(a,O.A.fa);return b.indexOf(c)>=0},Ov=["aw","dc","gb"],Qv=["aw","dc","gb","ag"],Pv=["aw","dc","gb","ag","gad_source"];function Tv(a){var b=N(a.D,J.m.Nc),c=N(a.D,J.m.Mc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Td&&M(131),a.isAborted=!0):!b&&c&&(M(132),a.isAborted=!0)}function Uv(a){var b=hp(J.m.U)?rp.pscdl:"denied";b!=null&&S(a,J.m.Jg,b)}function Vv(a){var b=El(!0);S(a,J.m.Lc,b)}function Wv(a){Dr()&&S(a,J.m.be,1)}
function Kv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Ik(a.substring(0,b))===void 0;)b--;return Ik(a.substring(0,b))||""}function Xv(a){Yv(a,zp.Df.Zm,N(a.D,J.m.pb))}function Yv(a,b,c){Jv(a,J.m.ud)||S(a,J.m.ud,{});Jv(a,J.m.ud)[b]=c}function Zv(a){R(a,O.A.Qf,Lm.X.Da)}function $v(a){var b=gb("GTAG_EVENT_FEATURE_CHANNEL");b&&(S(a,J.m.jf,b),eb())}function aw(a){var b=a.D.getMergedValues(J.m.uc);b&&a.mergeHitDataForKey(J.m.uc,b)}
function bw(a,b){b=b===void 0?!1:b;if(E(108)){var c=P(a,O.A.Pf);if(c)if(c.indexOf(a.target.destinationId)<0){if(R(a,O.A.Sj,!1),b||!cw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else R(a,O.A.Sj,!0)}}function dw(a){el&&(Qn=!0,a.eventName===J.m.qa?Wn(a.D,a.target.id):(P(a,O.A.Ke)||(Tn[a.target.id]=!0),yp(P(a,O.A.ib))))};function nw(a,b,c,d){var e=Dc(),f;if(e===1)a:{var g=bk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function zw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Jv(a,b)},setHitData:function(b,c){S(a,b,c)},setHitDataIfNotDefined:function(b,c){Jv(a,b)===void 0&&S(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return P(a,b)},setMetadata:function(b,c){R(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return N(a.D,b)},Ab:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return id(c)?a.mergeHitDataForKey(b,c):!1}}};var Bw=function(a){var b=Aw[a.target.destinationId];if(!a.isAborted&&b)for(var c=zw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Cw=function(a,b){var c=Aw[a];c||(c=Aw[a]=[]);c.push(b)},Aw={};function Ew(a,b){return arguments.length===1?Fw("set",a):Fw("set",a,b)}function Gw(a,b){return arguments.length===1?Fw("config",a):Fw("config",a,b)}function Hw(a,b,c){c=c||{};c[J.m.nd]=a;return Fw("event",b,c)}function Fw(){return arguments};var Jw=function(){this.messages=[];this.C=[]};Jw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Jw.prototype.listen=function(a){this.C.push(a)};
Jw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Jw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Kw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[O.A.ib]=hg.canonicalContainerId;Lw().enqueue(a,b,c)}
function Mw(){var a=Nw;Lw().listen(a)}function Lw(){return sp("mb",function(){return new Jw})};var Ow,Pw=!1;function Qw(){Pw=!0;Ow=Ow||{}}function Rw(a){Pw||Qw();return Ow[a]};function Sw(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Tw(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var Vw=function(a){var b=Uw(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},Uw=function(){var a=z.body,b=z.documentElement||a&&a.parentElement,c,d;if(z.compatMode&&z.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var Yw=function(a){if(Ww){if(a>=0&&a<Xw.length&&Xw[a]){var b;(b=Xw[a])==null||b.disconnect();Xw[a]=void 0}}else x.clearInterval(a)},ax=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(Ww){var e=!1;Jc(function(){e||Zw(a,b,c)()});return $w(function(f){e=!0;for(var g={fg:0};g.fg<f.length;g={fg:g.fg},g.fg++)Jc(function(h){return function(){a(f[h.fg])}}(g))},
b,c)}return x.setInterval(Zw(a,b,c),1E3)},Zw=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:zb()};Jc(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=Vw(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),
f[h]++;else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},$w=function(a,b,c){for(var d=new x.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<Xw.length;f++)if(!Xw[f])return Xw[f]=d,f;return Xw.push(d)-1},Xw=[],Ww=!(!x.IntersectionObserver||!x.IntersectionObserverEntry);
var cx=function(a){return a.tagName+":"+a.isVisible+":"+a.la.length+":"+bx.test(a.la)},qx=function(a){a=a||{Be:!0,Ce:!0,Jh:void 0};a.Zb=a.Zb||{email:!0,phone:!1,address:!1};var b=dx(a),c=ex[b];if(c&&zb()-c.timestamp<200)return c.result;var d=fx(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Zb&&a.Zb.email){var n=gx(d.elements);f=hx(n,a&&a.Vf);g=ix(f);n.length>10&&(e="3")}!a.Jh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(jx(f[p],!!a.Be,!!a.Ce));m=m.slice(0,10)}else if(a.Zb){}g&&(h=jx(g,!!a.Be,!!a.Ce));var F={elements:m,
Fj:h,status:e};ex[b]={timestamp:zb(),result:F};return F},rx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},tx=function(a){var b=sx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},sx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},px=function(a,b,c){var d=a.element,e={la:a.la,type:a.xa,tagName:d.tagName};b&&(e.querySelector=ux(d));c&&(e.isVisible=!Tw(d));return e},jx=function(a,b,c){return px({element:a.element,la:a.la,xa:ox.kc},b,c)},dx=function(a){var b=!(a==null||!a.Be)+"."+!(a==null||!a.Ce);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Zb&&(b+="."+a.Zb.email+"."+a.Zb.phone+"."+a.Zb.address);return b},ix=function(a){if(a.length!==0){var b;b=vx(a,function(c){return!wx.test(c.la)});b=vx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=vx(b,function(c){return!Tw(c.element)});return b[0]}},hx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&ri(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},vx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},ux=function(a){var b;if(a===z.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=ux(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},gx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(xx);if(f){var g=f[0],h;if(x.location){var m=Lk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,la:g})}}}return b},fx=function(){var a=[],b=z.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(yx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(zx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&Ax.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},xx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,bx=/@(gmail|googlemail)\./i,wx=/support|noreply/i,yx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),zx=
["BR"],Bx=sg('',2),ox={kc:"1",zd:"2",sd:"3",yd:"4",Je:"5",Nf:"6",kh:"7",Ri:"8",Mh:"9",Mi:"10"},ex={},Ax=["INPUT","SELECT"],Cx=sx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var ay=function(a,b,c){var d={};a.mergeHitDataForKey(J.m.Oi,(d[b]=c,d))},by=function(a,b){var c=cw(a,J.m.Og,a.D.H[J.m.Og]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},cy=function(a){var b=P(a,O.A.jb);if(id(b))return b},dy=function(a){if(P(a,O.A.xd)||!Xk(a.D))return!1;if(!N(a.D,J.m.od)){var b=N(a.D,J.m.Zd);return b===!0||b==="true"}return!0},ey=function(a){return cw(a,J.m.ce,N(a.D,J.m.ce))||!!cw(a,"google_ng",!1)};var dg;var fy=Number('')||5,gy=Number('')||50,hy=pb();
var jy=function(a,b){a&&(iy("sid",a.targetId,b),iy("cc",a.clientCount,b),iy("tl",a.totalLifeMs,b),iy("hc",a.heartbeatCount,b),iy("cl",a.clientLifeMs,b))},iy=function(a,b,c){b!=null&&c.push(a+"="+b)},ky=function(){var a=z.referrer;if(a){var b;return Jk(Pk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},ly="https://"+Ni(21,"www.googletagmanager.com")+"/a?",ny=function(){this.R=my;this.N=0};ny.prototype.H=function(a,b,c,d){var e=ky(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&iy("si",a.hg,g);iy("m",0,g);iy("iss",f,g);iy("if",c,g);jy(b,g);d&&iy("fm",encodeURIComponent(d.substring(0,gy)),g);this.P(g);};ny.prototype.C=function(a,b,c,d,e){var f=[];iy("m",1,f);iy("s",a,f);iy("po",ky(),f);b&&(iy("st",b.state,f),iy("si",b.hg,f),iy("sm",b.qg,f));jy(c,f);iy("c",d,f);e&&iy("fm",encodeURIComponent(e.substring(0,
gy)),f);this.P(f);};ny.prototype.P=function(a){a=a===void 0?[]:a;!dl||this.N>=fy||(iy("pid",hy,a),iy("bc",++this.N,a),a.unshift("ctid="+hg.ctid+"&t=s"),this.R(""+ly+a.join("&")))};var oy=Number('')||500,py=Number('')||5E3,qy=Number('20')||10,ry=Number('')||5E3;function sy(a){return a.performance&&a.performance.now()||Date.now()}
var ty=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{vm:function(){},wm:function(){},tm:function(){},onFailure:function(){}}:g;this.Eo=e;this.C=f;this.N=g;this.ba=this.ka=this.heartbeatCount=this.Co=0;this.mh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.hg=sy(this.C);this.qg=sy(this.C);this.R=10};d.prototype.init=function(){this.P(1);this.Ba()};d.prototype.getState=function(){return{state:this.state,
hg:Math.round(sy(this.C)-this.hg),qg:Math.round(sy(this.C)-this.qg)}};d.prototype.P=function(e){this.state!==e&&(this.state=e,this.qg=sy(this.C))};d.prototype.Ql=function(){return String(this.Co++)};d.prototype.Ba=function(){var e=this;this.heartbeatCount++;this.Xa({type:0,clientId:this.id,requestId:this.Ql(),maxDelay:this.nh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.ba++,f.isDead||e.ba>qy){var h=f.isDead&&f.failure.failureType;
e.R=h||10;e.P(4);e.Bo();var m,n;(n=(m=e.N).tm)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.P(3),e.Ul();else{if(e.heartbeatCount>f.stats.heartbeatCount+qy){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.N).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.P(2);if(r!==2)if(e.mh){var t,v;(v=(t=e.N).wm)==null||v.call(t)}else{e.mh=!0;var u,w;(w=(u=e.N).vm)==null||w.call(u)}e.ba=0;e.Fo();e.Ul()}}})};d.prototype.nh=function(){return this.state===2?
py:oy};d.prototype.Ul=function(){var e=this;this.C.setTimeout(function(){e.Ba()},Math.max(0,this.nh()-(sy(this.C)-this.ka)))};d.prototype.Io=function(e,f,g){var h=this;this.Xa({type:1,clientId:this.id,requestId:this.Ql(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,v;(v=(t=h.N).onFailure)==null||v.call(t,r);g(r)}})};d.prototype.Xa=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.R},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.C.setTimeout(function(){var r=g.H[m];r&&g.Lf(r,7)},(n=e.maxDelay)!=null?n:ry),q={request:e,Im:f,Cm:h,Yp:p};this.H[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.ka=sy(this.C);e.Cm=!1;this.Eo(e.request)};d.prototype.Fo=function(){for(var e=l(Object.keys(this.H)),f=e.next();!f.done;f=e.next()){var g=this.H[f.value];g.Cm&&this.sendRequest(g)}};d.prototype.Bo=function(){for(var e=
l(Object.keys(this.H)),f=e.next();!f.done;f=e.next())this.Lf(this.H[f.value],this.R)};d.prototype.Lf=function(e,f){this.Fb(e);var g=e.request;g.failure={failureType:f};e.Im(g)};d.prototype.Fb=function(e){delete this.H[e.request.requestId];this.C.clearTimeout(e.Yp)};d.prototype.Fp=function(e){this.ka=sy(this.C);var f=this.H[e.requestId];if(f)this.Fb(f),f.Im(e);else{var g,h;(h=(g=this.N).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,x,b);return c};var uy;
var vy=function(){uy||(uy=new ny);return uy},my=function(a){jn(ln(Lm.X.Qc),function(){Gc(a)})},wy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},xy=function(a){var b=a,c=Kj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},yy=function(a){var b=qn(mn.Z.Jl);return b&&b[a]},zy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.Wo(a);x.setTimeout(function(){f.initialize()},1E3);Jc(function(){f.Pp(a,b,e)})};k=zy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),hg:this.initTime,qg:Math.round(zb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Io(a,b,c)};k.getState=function(){return this.N.getState().state};k.Pp=function(a,b,c){var d=x.location.origin,e=this,
f=Ec();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?wy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Ec(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Fp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Wo=function(a){var b=this,c=ty(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{vm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},wm:function(){},tm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Ay(){var a=gg(dg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function By(a,b){var c=Math.round(zb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Ay()||E(168))return;jk()&&(a=""+d+ik()+"/_/service_worker");var e=xy(a);if(e===null||yy(e.origin))return;if(!rc()){vy().H(void 0,void 0,6);return}var f=new zy(e,!!a,c||Math.round(zb()),vy(),b);rn(mn.Z.Jl)[e.origin]=f;}
var Cy=function(a,b,c,d){var e;if((e=yy(a))==null||!e.delegate){var f=rc()?16:6;vy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}yy(a).delegate(b,c,d);};
function Dy(a,b,c,d,e){var f=xy();if(f===null){d(rc()?16:6);return}var g,h=(g=yy(f.origin))==null?void 0:g.initTime,m=Math.round(zb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Cy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ey(a,b,c,d){var e=xy(a);if(e===null){d("_is_sw=f"+(rc()?16:6)+"te");return}var f=b?1:0,g=Math.round(zb()),h,m=(h=yy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Cy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,v=(t=yy(e.origin))==
null?void 0:t.getState();v!==void 0&&(r+="s"+v);d(n?r+("t"+n):r+"te")});};function Fy(a){if(E(10)||jk()||Kj.N||Xk(a.D)||E(168))return;By(void 0,E(131));};var Gy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Hy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Iy(){var a=x.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Jy(){var a,b;return(b=(a=x.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Ky(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ly(){var a=x;if(!Ky(a))return null;var b=Hy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Gy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Ny=function(a,b){if(a)for(var c=My(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;S(b,f,c[f])}},My=function(a){var b={};b[J.m.uf]=a.architecture;b[J.m.vf]=a.bitness;a.fullVersionList&&(b[J.m.wf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[J.m.xf]=a.mobile?"1":"0";b[J.m.yf]=a.model;b[J.m.zf]=a.platform;b[J.m.Af]=a.platformVersion;b[J.m.Bf]=a.wow64?"1":"0";return b},Oy=function(a){var b=0,c=function(g,
h){try{a(g,h)}catch(m){}},d=Iy();if(d)c(d);else{var e=Jy();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=x.setTimeout(function(){c.ig||(c.ig=!0,M(106),c(null,Error("Timeout")))},b);e.then(function(g){c.ig||(c.ig=!0,M(104),x.clearTimeout(f),c(g))}).catch(function(g){c.ig||(c.ig=!0,M(105),x.clearTimeout(f),c(null,g))})}else c(null)}},Qy=function(){if(Ky(x)&&(Py=zb(),!Jy())){var a=Ly();a&&(a.then(function(){M(95)}),a.catch(function(){M(96)}))}},Py;function Ry(a){var b=a.location.href;if(a===a.top)return{url:b,Up:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Up:c}};var Fz=function(){return E(90)?jo():""},Gz=function(){var a;E(90)&&jo()!==""&&(a=jo());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Hz=function(){var a="www";E(90)&&jo()&&(a=jo());return"https://"+a+".google-analytics.com/g/collect"};function Iz(a,b){var c=!!jk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ik()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(187)?Fz()?Gz():""+ik()+"/ag/g/c":Fz().toLowerCase()==="region1"?""+ik()+"/r1ag/g/c":""+ik()+"/ag/g/c":Gz();case 16:if(c){if(E(187))return Fz()?Hz():
""+ik()+"/ga/g/c";var d=Fz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+ik()+d}return Hz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?ik()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ik()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Jo+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ik()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(E(207)?c:c&&b.Eh)?ik()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ik()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(E(207)?c:c&&b.Eh)?ik()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";
case 55:return c?ik()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":c?ik()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(E(207)?c:c&&b.Eh)?ik()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:ic(a,"Unknown endpoint")}};function Jz(a){a=a===void 0?[]:a;return Lj(a).join("~")}function Kz(){if(!E(118))return"";var a,b;return(((a=zm(om()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Lz(a,b){b&&sb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Nz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Jv(a,g),m=Mz[g];m&&h!==void 0&&h!==""&&(!P(a,O.A.se)||g!==J.m.dd&&g!==J.m.hd&&g!==J.m.Xd&&g!==J.m.Pe||(h="0"),d(m,h))}d("gtm",Ir({Ma:P(a,O.A.ib)}));tr()&&d("gcs",ur());d("gcd",yr(a.D));Br()&&d("dma_cps",zr());d("dma",Ar());Xq(er())&&d("tcfd",Cr());Jz()&&d("tag_exp",Jz());Kz()&&d("ptag_exp",Kz());if(P(a,O.A.ug)){d("tft",
zb());var n=Wc();n!==void 0&&d("tfd",Math.round(n))}E(24)&&d("apve","1");(E(25)||E(26))&&d("apvf",Tc()?E(26)?"f":"sb":"nf");cn[Lm.X.Da]!==Km.Ia.oe||fn[Lm.X.Da].isConsentGranted()||(c.limited_ads="1");b(c)},Oz=function(a,b,c){var d=b.D;Uo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Ya:{eventId:d.eventId,priorityId:d.priorityId},wh:{eventId:P(b,O.A.He),priorityId:P(b,O.A.Ie)}})},Pz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};Oz(a,b,c);em(d,a,void 0,{Hh:!0,method:"GET"},function(){},function(){dm(d,a+"&img=1")})},Qz=function(a){var b=yc()||wc()?"www.google.com":"www.googleadservices.com",c=[];sb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Rz=function(a){Nz(a,function(b){if(P(a,O.A.fa)===K.J.Ga){var c=[];E(28)&&a.target.destinationId&&c.push("tid="+a.target.destinationId);
sb(b,function(r,t){c.push(r+"="+t)});var d=hp([J.m.U,J.m.V])?45:46,e=Iz(d)+"?"+c.join("&");Oz(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(E(26)&&Tc()){em(g,e,void 0,{Hh:!0},function(){},function(){dm(g,e+"&img=1")});var h=hp([J.m.U,J.m.V]),m=Jv(a,J.m.md)==="1",n=Jv(a,J.m.Vh)==="1";if(h&&m&&!n){var p=Qz(b),q=yc()||wc()?58:57;Pz(p,a,q)}}else cm(g,e)||dm(g,e+"&img=1");if(jb(a.D.onSuccess))a.D.onSuccess()}})},Sz={},Mz=(Sz[J.m.ja]="gcu",
Sz[J.m.oc]="gclgb",Sz[J.m.mb]="gclaw",Sz[J.m.Ne]="gad_source",Sz[J.m.Oe]="gad_source_src",Sz[J.m.dd]="gclid",Sz[J.m.rk]="gclsrc",Sz[J.m.Pe]="gbraid",Sz[J.m.Xd]="wbraid",Sz[J.m.Qb]="auid",Sz[J.m.tk]="rnd",Sz[J.m.Vh]="ncl",Sz[J.m.Zh]="gcldc",Sz[J.m.hd]="dclid",Sz[J.m.Ub]="edid",Sz[J.m.kd]="en",Sz[J.m.ld]="gdpr",Sz[J.m.Vb]="gdid",Sz[J.m.be]="_ng",Sz[J.m.ff]="gpp_sid",Sz[J.m.hf]="gpp",Sz[J.m.jf]="_tu",Sz[J.m.Ok]="gtm_up",Sz[J.m.Lc]="frm",Sz[J.m.md]="lps",Sz[J.m.Ug]="did",Sz[J.m.Rk]="navt",Sz[J.m.Aa]=
"dl",Sz[J.m.Va]="dr",Sz[J.m.Db]="dt",Sz[J.m.Yk]="scrsrc",Sz[J.m.rf]="ga_uid",Sz[J.m.pd]="gdpr_consent",Sz[J.m.ni]="u_tz",Sz[J.m.Qa]="uid",Sz[J.m.Cf]="us_privacy",Sz[J.m.zc]="npa",Sz);var Tz={};Tz.O=Zr.O;var Uz={gr:"L",zo:"S",xr:"Y",Lq:"B",Vq:"E",Zq:"I",ur:"TC",Yq:"HTC"},Vz={zo:"S",Uq:"V",Oq:"E",rr:"tag"},Wz={},Xz=(Wz[Tz.O.Ti]="6",Wz[Tz.O.Ui]="5",Wz[Tz.O.Si]="7",Wz);function Yz(){function a(c,d){var e=gb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Zz=!1;function pA(a){}
function qA(a){}function rA(){}
function sA(a){}function tA(a){}
function uA(a){}
function vA(){}function wA(a,b){}
function xA(a,b,c){}
function yA(){};var zA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function AA(a,b,c,d,e,f,g){var h=Object.assign({},zA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var v;v=t.done;var u=p.decode(t.value,{stream:!v});BA(d,u);v?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():E(128)&&(b+="&_z=retryFetch",c?cm(a,b,c):bm(a,b))})};var CA=function(a){this.P=a;this.C=""},DA=function(a,b){a.H=b;return a},EA=function(a,b){a.N=b;return a},BA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}FA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},GA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};FA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},FA=function(a,b){b&&(HA(b.send_pixel,b.options,a.P),HA(b.create_iframe,b.options,a.H),HA(b.fetch,b.options,a.N))};function IA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function HA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=id(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var xB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),yB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},zB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},AB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function BB(){var a=pk("gtm.allowlist")||pk("gtm.whitelist");a&&M(9);Xj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);xB.test(x.location&&x.location.hostname)&&(Xj?M(116):(M(117),CB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Db(wb(a),yB),c=pk("gtm.blocklist")||pk("gtm.blacklist");c||(c=pk("tagTypeBlacklist"))&&M(3);c?M(8):c=[];xB.test(x.location&&x.location.hostname)&&(c=wb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
wb(c).indexOf("google")>=0&&M(2);var d=c&&Db(wb(c),zB),e={};return function(f){var g=f&&f[df.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=fk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Xj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){M(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=qb(d,h||[]);t&&M(10);q=t}}var v=!m||
q;!v&&(h.indexOf("sandboxedScripts")===-1?0:Xj&&h.indexOf("cmpPartners")>=0?!DB():b&&b.indexOf("sandboxedScripts")!==-1?0:qb(d,AB))&&(v=!0);return e[g]=v}}function DB(){var a=gg(dg.C,hg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var CB=!1;CB=!0;function EB(a,b,c,d,e){if(!FB()&&!Em(a)){d.loadExperiments=Mj();nm(a,d,e);var f=GB(a),g=function(){pm().container[a]&&(pm().container[a].state=3);HB()},h={destinationId:a,endpoint:0};if(jk())fm(h,ik()+"/"+f,void 0,g);else{var m=Eb(a,"GTM-"),n=Wk(),p=c?"/gtag/js":"/gtm.js",q=Vk(b,p+f);if(!q){var r=Oj.Ag+p;n&&tc&&m&&(r=tc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=nw("https://","http://",r+f)}fm(h,q,void 0,g)}}}
function HB(){Gm()||sb(Hm(),function(a,b){IB(a,b.transportUrl,b.context);M(92)})}
function IB(a,b,c,d){if(!FB()&&!Fm(a))if(c.loadExperiments||(c.loadExperiments=Mj()),Gm()){var e;(e=pm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:om()});pm().destination[a].state=0;qm({ctid:a,isDestination:!0},d);M(91)}else{var f;(f=pm().destination)[a]!=null||(f[a]={context:c,state:1,parent:om()});pm().destination[a].state=1;qm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(jk())fm(g,ik()+("/gtd"+GB(a,!0)));else{var h="/gtag/destination"+GB(a,!0),
m=Vk(b,h);m||(m=nw("https://","http://",Oj.Ag+h));fm(g,m)}}}function GB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Rj!=="dataLayer"&&(c+="&l="+Rj);if(!Eb(a,"GTM-")||b)c=E(130)?c+(jk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Jr();Wk()&&(c+="&sign="+Oj.Pi);var d=Kj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Mj().join("~")&&(c+="&tag_exp="+Mj().join("~"));return c}
function FB(){if(Er()){return!0}return!1};var JB=function(){this.H=0;this.C={}};JB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,hc:c};return d};JB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var LB=function(a,b){var c=[];sb(KB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.hc===void 0||b.indexOf(e.hc)>=0)&&c.push(e.listener)});return c};function MB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:hg.ctid}};function NB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var PB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;OB(this,a,b)},QB=function(a,b,c,d){if(Tj.hasOwnProperty(b)||b==="__zone")return-1;var e={};id(d)&&(e=jd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},RB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},SB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},OB=function(a,b,c){b!==void 0&&a.Sf(b);c&&x.setTimeout(function(){SB(a)},
Number(c))};PB.prototype.Sf=function(a){var b=this,c=Bb(function(){Jc(function(){a(hg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var TB=function(a){a.N++;return Bb(function(){a.H++;a.R&&a.H>=a.N&&SB(a)})},UB=function(a){a.R=!0;a.H>=a.N&&SB(a)};var VB={};function WB(){return x[XB()]}
function XB(){return x.GoogleAnalyticsObject||"ga"}function $B(){var a=hg.ctid;}
function aC(a,b){return function(){var c=WB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var gC=["es","1"],hC={},iC={};function jC(a,b){if(dl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";hC[a]=[["e",c],["eid",a]];uq(a)}}function kC(a){var b=a.eventId,c=a.Nd;if(!hC[b])return[];var d=[];iC[b]||d.push(gC);d.push.apply(d,ta(hC[b]));c&&(iC[b]=!0);return d};var lC={},mC={},nC={};function oC(a,b,c,d){dl&&E(120)&&((d===void 0?0:d)?(nC[b]=nC[b]||0,++nC[b]):c!==void 0?(mC[a]=mC[a]||{},mC[a][b]=Math.round(c)):(lC[a]=lC[a]||{},lC[a][b]=(lC[a][b]||0)+1))}function pC(a){var b=a.eventId,c=a.Nd,d=lC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete lC[b];return e.length?[["md",e.join(".")]]:[]}
function qC(a){var b=a.eventId,c=a.Nd,d=mC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete mC[b];return e.length?[["mtd",e.join(".")]]:[]}function rC(){for(var a=[],b=l(Object.keys(nC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+nC[d])}return a.length?[["mec",a.join(".")]]:[]};var sC={},tC={};function uC(a,b,c){if(dl&&b){var d=$k(b);sC[a]=sC[a]||[];sC[a].push(c+d);var e=b[df.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Gf[e]?"1":"2")+d;tC[a]=tC[a]||[];tC[a].push(f);uq(a)}}function vC(a){var b=a.eventId,c=a.Nd,d=[],e=sC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=tC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete sC[b],delete tC[b]);return d};function wC(a,b,c){c=c===void 0?!1:c;xC().addRestriction(0,a,b,c)}function yC(a,b,c){c=c===void 0?!1:c;xC().addRestriction(1,a,b,c)}function zC(){var a=wm();return xC().getRestrictions(1,a)}var AC=function(){this.container={};this.C={}},BC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
AC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=BC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
AC.prototype.getRestrictions=function(a,b){var c=BC(this,b);if(a===0){var d,e;return[].concat(ta((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ta((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ta((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ta((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
AC.prototype.getExternalRestrictions=function(a,b){var c=BC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};AC.prototype.removeExternalRestrictions=function(a){var b=BC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function xC(){return sp("r",function(){return new AC})};function CC(a,b,c,d){var e=Ef[a],f=DC(a,b,c,d);if(!f)return null;var g=Tf(e[df.Kl],c,[]);if(g&&g.length){var h=g[0];f=CC(h.index,{onSuccess:f,onFailure:h.hm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function DC(a,b,c,d){function e(){function w(){Xn(3);var L=zb()-H;uC(c.id,f,"7");RB(c.Rc,D,"exception",L);E(109)&&xA(c,f,Tz.O.Si);F||(F=!0,h())}if(f[df.qo])h();else{var y=Sf(f,c,[]),A=y[df.Wm];if(A!=null)for(var C=0;C<A.length;C++)if(!hp(A[C])){h();return}var D=QB(c.Rc,String(f[df.Ra]),Number(f[df.qh]),y[df.METADATA]),F=!1;y.vtp_gtmOnSuccess=function(){if(!F){F=!0;var L=zb()-H;uC(c.id,Ef[a],"5");RB(c.Rc,D,"success",L);E(109)&&xA(c,f,Tz.O.Ui);g()}};y.vtp_gtmOnFailure=function(){if(!F){F=!0;var L=zb()-
H;uC(c.id,Ef[a],"6");RB(c.Rc,D,"failure",L);E(109)&&xA(c,f,Tz.O.Ti);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);uC(c.id,f,"1");E(109)&&wA(c,f);var H=zb();try{Uf(y,{event:c,index:a,type:1})}catch(L){w(L)}E(109)&&xA(c,f,Tz.O.Rl)}}var f=Ef[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Tf(f[df.Sl],c,[]);if(n&&n.length){var p=n[0],q=CC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.hm===
2?m:q}if(f[df.Bl]||f[df.so]){var r=f[df.Bl]?Ff:c.Dq,t=g,v=h;if(!r[a]){var u=EC(a,r,Bb(e));g=u.onSuccess;h=u.onFailure}return function(){r[a](t,v)}}return e}function EC(a,b,c){var d=[],e=[];b[a]=FC(d,e,c);return{onSuccess:function(){b[a]=GC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=HC;for(var f=0;f<e.length;f++)e[f]()}}}function FC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function GC(a){a()}function HC(a,b){b()};var KC=function(a,b){for(var c=[],d=0;d<Ef.length;d++)if(a[d]){var e=Ef[d];var f=TB(b.Rc);try{var g=CC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[df.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Gf[h];c.push({Om:d,priorityOverride:(m?m.priorityOverride||0:0)||NB(e[df.Ra],1)||0,execute:g})}else IC(d,b),f()}catch(p){f()}}c.sort(JC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function LC(a,b){if(!KB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=LB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=TB(b);try{d[e](a,f)}catch(g){f()}}return!0}function JC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Om,h=b.Om;f=g>h?1:g<h?-1:0}return f}
function IC(a,b){if(dl){var c=function(d){var e=b.isBlocked(Ef[d])?"3":"4",f=Tf(Ef[d][df.Kl],b,[]);f&&f.length&&c(f[0].index);uC(b.id,Ef[d],e);var g=Tf(Ef[d][df.Sl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var MC=!1,KB;function NC(){KB||(KB=new JB);return KB}
function OC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(MC)return!1;MC=!0}var e=!1,f=zC(),g=jd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}jC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:PC(g,e),Dq:[],logMacroError:function(){M(6);Xn(0)},cachedModelValues:QC(),Rc:new PB(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&dl&&(n.reportMacroDiscrepancy=oC);E(109)&&tA(n.id);var p=Zf(n);E(109)&&uA(n.id);e&&(p=RC(p));E(109)&&sA(b);var q=KC(p,n),r=LC(a,n.Rc);UB(n.Rc);d!=="gtm.js"&&d!=="gtm.sync"||$B();return SC(p,q)||r}function QC(){var a={};a.event=uk("event",1);a.ecommerce=uk("ecommerce",1);a.gtm=uk("gtm");a.eventModel=uk("eventModel");return a}
function PC(a,b){var c=BB();return function(d){if(c(d))return!0;var e=d&&d[df.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=wm();f=xC().getRestrictions(0,g);var h=a;b&&(h=jd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=fk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function RC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Ef[c][df.Ra]);if(Sj[d]||Ef[c][df.uo]!==void 0||NB(d,2))b[c]=!0}return b}function SC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Ef[c]&&!Tj[String(Ef[c][df.Ra])])return!0;return!1};function TC(){NC().addListener("gtm.init",function(a,b){Kj.ba=!0;Hn();b()})};var UC=!1,VC=0,WC=[];function XC(a){if(!UC){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){UC=!0;for(var e=0;e<WC.length;e++)Jc(WC[e])}WC.push=function(){for(var f=xa.apply(0,arguments),g=0;g<f.length;g++)Jc(f[g]);return 0}}}function YC(){if(!UC&&VC<140){VC++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");XC()}catch(c){x.setTimeout(YC,50)}}}
function ZC(){UC=!1;VC=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")XC();else{Hc(z,"DOMContentLoaded",XC);Hc(z,"readystatechange",XC);if(z.createEventObject&&z.documentElement.doScroll){var a=!0;try{a=!x.frameElement}catch(b){}a&&YC()}Hc(x,"load",XC)}}function $C(a){UC?a():WC.push(a)};var aD={},bD={};function cD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Ej:void 0,mj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Ej=Cp(g,b),e.Ej){var h=vm();ob(h,function(r){return function(t){return r.Ej.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=aD[g]||[];e.mj={};m.forEach(function(r){return function(t){r.mj[t]=!0}}(e));for(var n=xm(),p=0;p<n.length;p++)if(e.mj[n[p]]){c=c.concat(vm());break}var q=bD[g]||[];q.length&&(c=c.concat(q))}}return{yj:c,aq:d}}
function dD(a){sb(aD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function eD(a){sb(bD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var fD=!1,gD=!1;function hD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=jd(b,null),b[J.m.df]&&(d.eventCallback=b[J.m.df]),b[J.m.Pg]&&(d.eventTimeout=b[J.m.Pg]));return d}function iD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:vp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function jD(a,b){var c=a&&a[J.m.nd];c===void 0&&(c=pk(J.m.nd,2),c===void 0&&(c="default"));if(lb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?lb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=cD(d,b.isGtmEvent),f=e.yj,g=e.aq;if(g.length)for(var h=kD(a),m=0;m<g.length;m++){var n=Cp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=pm().destination[q];r&&r.state===0||IB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{yj:Dp(f,b.isGtmEvent),
Ko:Dp(t,b.isGtmEvent)}}}var lD=void 0,mD=void 0;function nD(a,b,c){var d=jd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&M(136);var e=jd(b,null);jd(c,e);Kw(Gw(xm()[0],e),a.eventId,d)}function kD(a){for(var b=l([J.m.od,J.m.xc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Cq.C[d];if(e)return e}}
var oD={config:function(a,b){var c=iD(a,b);if(!(a.length<2)&&lb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!id(a[2])||a.length>3)return;d=a[2]}var e=Cp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!tm.pe){var m=zm(om());if(Im(m)){var n=m.parent,p=n.isDestination;h={cq:zm(n),Wp:p};break a}}h=void 0}var q=h;q&&(f=q.cq,g=q.Wp);jC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?vm().indexOf(r)===-1:xm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Nc]){var v=kD(d);if(t)IB(r,v,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var u=d;lD?nD(b,u,lD):mD||(mD=jd(u,null))}else EB(r,v,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(M(128),g&&M(130),b.inheritParentConfig)){var w;var y=d;mD?(nD(b,mD,y),w=!1):(!y[J.m.rd]&&Vj&&lD||(lD=jd(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}el&&(xp===1&&(zn.mcc=!1),xp=2);if(Vj&&!t&&!d[J.m.rd]){var A=gD;gD=!0;if(A)return}fD||M(43);if(!b.noTargetGroup)if(t){eD(e.id);
var C=e.id,D=d[J.m.Sg]||"default";D=String(D).split(",");for(var F=0;F<D.length;F++){var H=bD[D[F]]||[];bD[D[F]]=H;H.indexOf(C)<0&&H.push(C)}}else{dD(e.id);var L=e.id,Q=d[J.m.Sg]||"default";Q=Q.toString().split(",");for(var ca=0;ca<Q.length;ca++){var U=aD[Q[ca]]||[];aD[Q[ca]]=U;U.indexOf(L)<0&&U.push(L)}}delete d[J.m.Sg];var qa=b.eventMetadata||{};qa.hasOwnProperty(O.A.wd)||(qa[O.A.wd]=!b.fromContainerExecution);b.eventMetadata=qa;delete d[J.m.df];for(var T=t?[e.id]:vm(),Z=0;Z<T.length;Z++){var Y=
d,V=T[Z],ka=jd(b,null),ia=Cp(V,ka.isGtmEvent);ia&&Cq.push("config",[Y],ia,ka)}}}}},consent:function(a,b){if(a.length===3){M(39);var c=iD(a,b),d=a[1],e={},f=Ao(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.vg?Array.isArray(h)?NaN:Number(h):g===J.m.jc?(Array.isArray(h)?h:[h]).map(Bo):Co(h)}b.fromContainerExecution||(e[J.m.V]&&M(139),e[J.m.La]&&M(140));d==="default"?dp(e):d==="update"?fp(e,c):d==="declare"&&b.fromContainerExecution&&cp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&lb(c)){var d=void 0;if(a.length>2){if(!id(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=hD(c,d),f=iD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=jD(d,b);if(m){var n=m.yj,p=m.Ko,q,r,t;if(E(108)){q=p.map(function(L){return L.id});r=p.map(function(L){return L.destinationId});t=n.map(function(L){return L.id});for(var v=l(vm()),u=v.next();!u.done;u=v.next()){var w=u.value;r.indexOf(w)<
0&&t.push(w)}}else q=n.map(function(L){return L.id}),r=n.map(function(L){return L.destinationId}),t=q;jC(g,c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var C=A.value,D=jd(b,null),F=jd(d,null);delete F[J.m.df];var H=D.eventMetadata||{};H.hasOwnProperty(O.A.wd)||(H[O.A.wd]=!D.fromContainerExecution);H[O.A.Ni]=q.slice();H[O.A.Pf]=r.slice();D.eventMetadata=H;Dq(c,F,C,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.nd]=q.join(","):delete e.eventModel[J.m.nd];fD||M(43);b.noGtmEvent===void 0&&
b.eventMetadata&&b.eventMetadata[O.A.Pl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Mc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){M(53);if(a.length===4&&lb(a[1])&&lb(a[2])&&jb(a[3])){var c=Cp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){fD||M(43);var f=kD();if(ob(vm(),function(h){return c.destinationId===h})){iD(a,b);var g={};jd((g[J.m.sc]=d,g[J.m.Kc]=e,g),null);Eq(d,function(h){Jc(function(){e(h)})},c.id,b)}else IB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},
js:function(a,b){if(a.length===2&&a[1].getTime){fD=!0;var c=iD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&lb(a[1])&&jb(a[2])){if(eg(a[1],a[2]),M(74),a[1]==="all"){M(75);var b=!1;try{b=a[2](hg.ctid,"unknown",{})}catch(c){}b||M(76)}}else M(73)},set:function(a,b){var c=void 0;a.length===2&&id(a[1])?c=jd(a[1],null):a.length===3&&lb(a[1])&&(c={},id(a[2])||Array.isArray(a[2])?
c[a[1]]=jd(a[2],null):c[a[1]]=a[2]);if(c){var d=iD(a,b),e=d.eventId,f=d.priorityId;jd(c,null);var g=jd(c,null);Cq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},pD={policy:!0};var rD=function(a){if(qD(a))return a;this.value=a};rD.prototype.getUntrustedMessageValue=function(){return this.value};var qD=function(a){return!a||gd(a)!=="object"||id(a)?!1:"getUntrustedMessageValue"in a};rD.prototype.getUntrustedMessageValue=rD.prototype.getUntrustedMessageValue;var sD=!1,tD=[];function uD(){if(!sD){sD=!0;for(var a=0;a<tD.length;a++)Jc(tD[a])}}function vD(a){sD?Jc(a):tD.push(a)};var wD=0,xD={},yD=[],zD=[],AD=!1,BD=!1;function CD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function DD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return ED(a)}function FD(a,b){if(!mb(b)||b<0)b=0;var c=rp[Rj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function GD(a,b){var c=a._clear||b.overwriteModelFields;sb(a,function(e,f){e!=="_clear"&&(c&&sk(e),sk(e,f))});ck||(ck=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=vp(),a["gtm.uniqueEventId"]=d,sk("gtm.uniqueEventId",d));return OC(a)}function HD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(tb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function ID(){var a;if(zD.length)a=zD.shift();else if(yD.length)a=yD.shift();else return;var b;var c=a;if(AD||!HD(c.message))b=c;else{AD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=vp(),f=vp(),c.message["gtm.uniqueEventId"]=vp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};yD.unshift(n,c);b=h}return b}
function JD(){for(var a=!1,b;!BD&&(b=ID());){BD=!0;delete mk.eventModel;ok();var c=b,d=c.message,e=c.messageContext;if(d==null)BD=!1;else{e.fromContainerExecution&&tk();try{if(jb(d))try{d.call(qk)}catch(v){}else if(Array.isArray(d)){if(lb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=pk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(v){}}}else{var n=void 0;if(tb(d))a:{if(d.length&&lb(d[0])){var p=oD[d[0]];if(p&&(!e.fromContainerExecution||!pD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=GD(n,e)||a)}}finally{e.fromContainerExecution&&ok(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=xD[String(q)]||[],t=0;t<r.length;t++)zD.push(KD(r[t]));r.length&&zD.sort(CD);delete xD[String(q)];q>wD&&(wD=q)}BD=!1}}}return!a}
function LD(){if(E(109)){var a=!Kj.ka;}var c=JD();if(E(109)){}try{var e=hg.ctid,f=x[Rj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Nw(a){if(wD<a.notBeforeEventId){var b=String(a.notBeforeEventId);xD[b]=xD[b]||[];xD[b].push(a)}else zD.push(KD(a)),zD.sort(CD),Jc(function(){BD||JD()})}function KD(a){return{message:a.message,messageContext:a.messageContext}}
function MD(){function a(f){var g={};if(qD(f)){var h=f;f=qD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=uc(Rj,[]),c=rp[Rj]=rp[Rj]||{};c.pruned===!0&&M(83);xD=Lw().get();Mw();$C(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});vD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(rp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new rD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});yD.push.apply(yD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(M(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return JD()&&p};var e=b.slice(0).map(function(f){return a(f)});yD.push.apply(yD,e);if(!Kj.ka){if(E(109)){}Jc(LD)}}var ED=function(a){return x[Rj].push(a)};function ND(a){ED(a)};function OD(){var a,b=Pk(x.location.href);(a=b.hostname+b.pathname)&&Dn("dl",encodeURIComponent(a));var c;var d=hg.ctid;if(d){var e=tm.pe?1:0,f,g=zm(om());f=g&&g.context;c=d+";"+hg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Dn("tdp",h);var m=El(!0);m!==void 0&&Dn("frm",String(m))};function PD(){(No()||el)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){M(179);var b=am(a.effectiveDirective);if(b){var c;var d=Zl(b,a.blockedURI);c=d?Xl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(u){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Gm){p.Gm=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(No()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(No()){var v=To("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});v.tagDiagnostics=t;Mo(v)}}}Jn(p.endpoint)}}$l(b,a.blockedURI)}}}}})};function QD(){var a;var b=ym();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Dn("pcid",e)};var RD=/^(https?:)?\/\//;
function SD(){var a=Am();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=Yc())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(RD,"")===d.replace(RD,""))){b=g;break a}}M(146)}else M(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Dn("rtg",String(a.canonicalContainerId)),Dn("slo",String(p)),Dn("hlo",a.htmlLoadOrder||"-1"),
Dn("lst",String(a.loadScriptType||"0")))}else M(144)};function TD(){var a=[],b=Number('1')||0,c=function(){var f=!1;return f}();a.push({Nm:195,Mm:195,experimentId:104527906,controlId:104527907,percent:b,active:c,dj:1});var d=Number('1')||0,e=function(){var f=!1;
return f}();a.push({Nm:196,Mm:196,experimentId:104528500,controlId:104528501,percent:d,active:e,dj:0});return a};var UD={};function VD(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Kj.R.H.add(Number(c.value))}function WD(){for(var a=l(TD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Nm;ii[d]=c;if(c.dj===1){var e=d,f=rn(mn.Z.wo);li(f,e);VD(f)}else if(c.dj===0){var g=UD;li(g,d);VD(g)}}};

function qE(){};var rE=function(){};rE.prototype.toString=function(){return"undefined"};var sE=new rE;function zE(a,b){function c(g){var h=Pk(g),m=Jk(h,"protocol"),n=Jk(h,"host",!0),p=Jk(h,"port"),q=Jk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function AE(a){return BE(a)?1:0}
function BE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=jd(a,{});jd({arg1:c[d],any_of:void 0},e);if(AE(e))return!0}return!1}switch(a["function"]){case "_cn":return Ng(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Ig.length;g++){var h=Ig[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Jg(b,c);case "_eq":return Og(b,c);case "_ge":return Pg(b,c);case "_gt":return Rg(b,c);case "_lc":return Kg(b,c);case "_le":return Qg(b,
c);case "_lt":return Sg(b,c);case "_re":return Mg(b,c,a.ignore_case);case "_sw":return Tg(b,c);case "_um":return zE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var CE=function(a,b,c,d){Tq.call(this);this.mh=b;this.Lf=c;this.Fb=d;this.Xa=new Map;this.nh=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};ra(CE,Tq);CE.prototype.N=function(){delete this.C;this.Xa.clear();this.ka.clear();this.Ba.clear();this.R&&(Pq(this.H,"message",this.R),delete this.R);delete this.H;delete this.Fb;Tq.prototype.N.call(this)};
var DE=function(a){if(a.C)return a.C;a.Lf&&a.Lf(a.H)?a.C=a.H:a.C=Dl(a.H,a.mh);var b;return(b=a.C)!=null?b:null},FE=function(a,b,c){if(DE(a))if(a.C===a.H){var d=a.Xa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.xj){EE(a);var f=++a.nh;a.Ba.set(f,{Ih:e.Ih,Zo:e.qm(c),persistent:b==="addEventListener"});a.C.postMessage(e.xj(c,f),"*")}}},EE=function(a){a.R||(a.R=function(b){try{var c;c=a.Fb?a.Fb(b):void 0;if(c){var d=c.hq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Ih)==null||f.call(e,
e.Zo,c.payload)}}}catch(g){}},Oq(a.H,"message",a.R))};var GE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},HE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},IE={qm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Ih:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},JE={qm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Ih:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function KE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,hq:b.__gppReturn.callId}}
var LE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Tq.call(this);this.caller=new CE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},KE);this.caller.Xa.set("addEventListener",GE);this.caller.ka.set("addEventListener",IE);this.caller.Xa.set("removeEventListener",HE);this.caller.ka.set("removeEventListener",JE);this.timeoutMs=c!=null?c:500};ra(LE,Tq);LE.prototype.N=function(){this.caller.dispose();Tq.prototype.N.call(this)};
LE.prototype.addEventListener=function(a){var b=this,c=gl(function(){a(ME,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);FE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(NE,!0);return}a(OE,!0)}}})};
LE.prototype.removeEventListener=function(a){FE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var OE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},ME={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},NE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function PE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){tv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");tv.C=d}}function QE(){try{var a=new LE(x,{timeoutMs:-1});DE(a.caller)&&a.addEventListener(PE)}catch(b){}};function RE(){var a=[["cv",Oi(1)],["rv",Pj],["tc",Ef.filter(function(b){return b}).length]];Qj&&a.push(["x",Qj]);hk()&&a.push(["tag_exp",hk()]);return a};var SE={};function Ri(a){SE[a]=(SE[a]||0)+1}function TE(){for(var a=[],b=l(Object.keys(SE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+SE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var UE={},VE={};function WE(a){var b=a.eventId,c=a.Nd,d=[],e=UE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=VE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete UE[b],delete VE[b]);return d};function XE(){return!1}function YE(){var a={};return function(b,c,d){}};function ZE(){var a=$E;return function(b,c,d){var e=d&&d.event;aF(c);var f=yh(b)?void 0:1,g=new Ua;sb(c,function(r,t){var v=zd(t,void 0,f);v===void 0&&t!==void 0&&M(44);g.set(r,v)});a.Ob(Xf());var h={Yl:lg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Rc.Sf(r)}:void 0,Kb:function(){return b},log:function(){},mp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},oq:!!NB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(XE()){var m=YE(),n,p;h.wb={Oj:[],Tf:{},bc:function(r,t,v){t===1&&(n=r);t===7&&(p=v);m(r,t,v)},Gh:Qh()};h.log=function(r){var t=xa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=Ve(a,h,[b,g]);a.Ob();q instanceof Aa&&(q.type==="return"?q=q.data:q=void 0);return yd(q,void 0,f)}}function aF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;jb(b)&&(a.gtmOnSuccess=function(){Jc(b)});jb(c)&&(a.gtmOnFailure=function(){Jc(c)})};function bF(a){}bF.M="internal.addAdsClickIds";function cF(a,b){var c=this;}cF.publicName="addConsentListener";var dF=!1;function eF(a){for(var b=0;b<a.length;++b)if(dF)try{a[b]()}catch(c){M(77)}else a[b]()}function fF(a,b,c){var d=this,e;if(!jh(a)||!fh(b)||!kh(c))throw G(this.getName(),["string","function","string|undefined"],arguments);eF([function(){I(d,"listen_data_layer",a)}]);e=NC().addListener(a,yd(b),c===null?void 0:c);return e}fF.M="internal.addDataLayerEventListener";function gF(a,b,c){}gF.publicName="addDocumentEventListener";function hF(a,b,c,d){}hF.publicName="addElementEventListener";function iF(a){return a.K.sb()};function jF(a){}jF.publicName="addEventCallback";
var kF=function(a){return typeof a==="string"?a:String(vp())},nF=function(a,b){lF(a,"init",!1)||(mF(a,"init",!0),b())},lF=function(a,b,c){var d=oF(a);return Ab(d,b,c)},pF=function(a,b,c,d){var e=oF(a),f=Ab(e,b,d);e[b]=c(f)},mF=function(a,b,c){oF(a)[b]=c},oF=function(a){var b=sp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},qF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Vc(a,"className"),"gtm.elementId":a.for||Kc(a,"id")||"","gtm.elementTarget":a.formTarget||
Vc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Vc(a,"href")||a.src||a.code||a.codebase||"";return d};
var tF=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e],h=g.tagName.toLowerCase();if(!(rF.indexOf(h)<0||h==="input"&&sF.indexOf(g.type.toLowerCase())>=0)){if(g.dataset[c]===d)return f;f++}}return 0},uF=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:z.getElementById(a.form)}return Nc(a,["form"],100)},rF=["input","select","textarea"],sF=["button","hidden","image","reset","submit"];
function yF(a){}yF.M="internal.addFormAbandonmentListener";function zF(a,b,c,d){}
zF.M="internal.addFormData";var AF={},BF=[],CF={},DF=0,EF=0;
var GF=function(){Hc(z,"change",function(a){for(var b=0;b<BF.length;b++)BF[b](a)});Hc(x,"pagehide",function(){FF()})},FF=function(){sb(CF,function(a,b){var c=AF[a];c&&sb(b,function(d,e){HF(e,c)})})},KF=function(a,b){var c=""+a;if(AF[c])AF[c].push(b);else{var d=[b];AF[c]=d;var e=CF[c];e||(e={},CF[c]=e);BF.push(function(f){var g=f.target;if(g){var h=uF(g);if(h){var m=IF(h,"gtmFormInteractId",function(){return DF++}),n=IF(g,"gtmFormInteractFieldId",function(){return EF++}),p=e[m];p?(p.Gc&&(x.clearTimeout(p.Gc),
p.fc.dataset.gtmFormInteractFieldId!==n&&HF(p,d)),p.fc=g,JF(p,d,a)):(e[m]={form:h,fc:g,sequenceNumber:0,Gc:null},JF(e[m],d,a))}}})}},HF=function(a,b){var c=a.form,d=a.fc,e=qF(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=tF(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.Gc=null},JF=function(a,b,c){c?a.Gc=x.setTimeout(function(){HF(a,b)},c):HF(a,b)},IF=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function LF(a,b){if(!fh(a)||!dh(b))throw G(this.getName(),["function","Object|undefined"],arguments);var c=yd(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=yd(a),f;lF("pix.fil","init")?f=lF("pix.fil","reg"):(GF(),f=KF,mF("pix.fil","reg",KF),mF("pix.fil","init",!0));f(d,e);}LF.M="internal.addFormInteractionListener";
var NF=function(a,b,c){var d=qF(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&MF(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},OF=function(a,b){var c=lF("pix.fsl",a?"nv.mwt":"mwt",0);x.setTimeout(b,c)},PF=function(a,b,c,d,e){var f=lF("pix.fsl",c?"nv.mwt":"mwt",0),g=lF("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var h=NF(a,c,e);M(121);if(h["gtm.elementUrl"]==="https://www.facebook.com/tr/")return M(122),!0;if(d&&f){for(var m=Jb(b,g.length),n=0;n<g.length;++n)g[n](h,m);return m.done}for(var p=0;p<g.length;++p)g[p](h,function(){});return!0},QF=function(){var a=[],b=function(c){return ob(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
MF=function(a){var b=Vc(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},RF=function(){var a=QF(),b=HTMLFormElement.prototype.submit;Hc(z,"click",function(c){var d=c.target;if(d){var e=Nc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&Kc(e,"value")){var f=uF(e);f&&a.store(f,e)}}},!1);Hc(z,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=MF(d)&&!e,g=a.get(d),h=!0,m=function(){if(h){var n,
p={};g&&(n=z.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),hc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&hc(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(PF(d,m,e,f,g))return h=!1,c.returnValue;OF(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};PF(c,e,!1,MF(c))?(b.call(c),d=!1):OF(!1,e)}};
function SF(a,b){if(!fh(a)||!dh(b))throw G(this.getName(),["function","Object|undefined"],arguments);var c=yd(b,this.K,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=yd(a,this.K,1);if(d){var h=function(n){return Math.max(e,n)};pF("pix.fsl","mwt",h,0);f||pF("pix.fsl","nv.mwt",h,0)}var m=function(n){n.push(g);return n};pF("pix.fsl","runIfUncanceled",m,[]);f||pF("pix.fsl","runIfCanceled",
m,[]);lF("pix.fsl","init")||(RF(),mF("pix.fsl","init",!0));}SF.M="internal.addFormSubmitListener";
function XF(a){}XF.M="internal.addGaSendListener";function YF(a){if(!a)return{};var b=a.mp;return MB(b.type,b.index,b.name)}function ZF(a){return a?{originatingEntity:YF(a)}:{}};function gG(a){var b=rp.zones;return b?b.getIsAllowedFn(xm(),a):function(){return!0}}function hG(){var a=rp.zones;a&&a.unregisterChild(xm())}
function iG(){yC(wm(),function(a){var b=rp.zones;return b?b.isActive(xm(),a.originalEventData["gtm.uniqueEventId"]):!0});wC(wm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return gG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var jG=function(a,b){this.tagId=a;this.we=b};
function kG(a,b){var c=this;return a}kG.M="internal.loadGoogleTag";function lG(a){return new qd("",function(b){var c=this.evaluate(b);if(c instanceof qd)return new qd("",function(){var d=xa.apply(0,arguments),e=this,f=jd(iF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.rb();h.Ld(f);return c.Mb.apply(c,[h].concat(ta(g)))})})};function mG(a,b,c){var d=this;}mG.M="internal.addGoogleTagRestriction";var nG={},oG=[];
function vG(a,b){}
vG.M="internal.addHistoryChangeListener";function wG(a,b,c){}wG.publicName="addWindowEventListener";function xG(a,b){return!0}xG.publicName="aliasInWindow";function yG(a,b,c){}yG.M="internal.appendRemoteConfigParameter";function zG(a){var b;return b}
zG.publicName="callInWindow";function AG(a){}AG.publicName="callLater";function BG(a){}BG.M="callOnDomReady";function CG(a){}CG.M="callOnWindowLoad";function DG(a,b){var c;return c}DG.M="internal.computeGtmParameter";function EG(a,b){var c=this;}EG.M="internal.consentScheduleFirstTry";function FG(a,b){var c=this;}FG.M="internal.consentScheduleRetry";function GG(a){var b;return b}GG.M="internal.copyFromCrossContainerData";function HG(a,b){var c;var d=zd(c,this.K,yh(iF(this).Kb())?2:1);d===void 0&&c!==void 0&&M(45);return d}HG.publicName="copyFromDataLayer";
function IG(a){var b=void 0;return b}IG.M="internal.copyFromDataLayerCache";function JG(a){var b;return b}JG.publicName="copyFromWindow";function KG(a){var b=void 0;return zd(b,this.K,1)}KG.M="internal.copyKeyFromWindow";var LG=function(a){return a===Lm.X.Da&&cn[a]===Km.Ia.oe&&!hp(J.m.U)};var MG=function(){return"0"},NG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Qk(a,b,"0")};var OG={},PG={},QG={},RG={},SG={},TG={},UG={},VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH=(mH[J.m.Qa]=(OG[2]=[LG],OG),mH[J.m.rf]=(PG[2]=[LG],PG),mH[J.m.ef]=(QG[2]=[LG],QG),mH[J.m.si]=(RG[2]=[LG],RG),mH[J.m.ui]=(SG[2]=[LG],SG),mH[J.m.wi]=(TG[2]=[LG],TG),mH[J.m.xi]=(UG[2]=[LG],UG),mH[J.m.yi]=(VG[2]=[LG],VG),mH[J.m.yc]=(WG[2]=[LG],WG),mH[J.m.uf]=(XG[2]=[LG],XG),mH[J.m.vf]=(YG[2]=[LG],YG),mH[J.m.wf]=(ZG[2]=[LG],ZG),mH[J.m.xf]=($G[2]=
[LG],$G),mH[J.m.yf]=(aH[2]=[LG],aH),mH[J.m.zf]=(bH[2]=[LG],bH),mH[J.m.Af]=(cH[2]=[LG],cH),mH[J.m.Bf]=(dH[2]=[LG],dH),mH[J.m.mb]=(eH[1]=[LG],eH),mH[J.m.dd]=(fH[1]=[LG],fH),mH[J.m.hd]=(gH[1]=[LG],gH),mH[J.m.Xd]=(hH[1]=[LG],hH),mH[J.m.Pe]=(iH[1]=[function(a){return E(102)&&LG(a)}],iH),mH[J.m.jd]=(jH[1]=[LG],jH),mH[J.m.Aa]=(kH[1]=[LG],kH),mH[J.m.Va]=(lH[1]=[LG],lH),mH),oH={},pH=(oH[J.m.mb]=MG,oH[J.m.dd]=MG,oH[J.m.hd]=MG,oH[J.m.Xd]=MG,oH[J.m.Pe]=MG,oH[J.m.jd]=function(a){if(!id(a))return{};var b=jd(a,
null);delete b.match_id;return b},oH[J.m.Aa]=NG,oH[J.m.Va]=NG,oH),qH={},rH={},sH=(rH[O.A.jb]=(qH[2]=[LG],qH),rH),tH={};var uH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};uH.prototype.getValue=function(a){a=a===void 0?Lm.X.Gb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};uH.prototype.H=function(){return gd(this.C)==="array"||id(this.C)?jd(this.C,null):this.C};
var vH=function(){},wH=function(a,b){this.conditions=a;this.C=b},xH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new uH(c,e,g,a.C[b]||vH)},yH,zH;var AH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;R(this,g,d[g])}},Jv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,P(a,O.A.Qf))},S=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(yH!=null||(yH=new wH(nH,pH)),e=xH(yH,b,c));d[b]=e};
AH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return S(this,a,b),!0;if(!id(c))return!1;S(this,a,Object.assign(c,b));return!0};var BH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
AH.prototype.copyToHitData=function(a,b,c){var d=N(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&lb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&S(this,a,d)};
var P=function(a,b){var c=a.metadata[b];if(b===O.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,P(a,O.A.Qf))},R=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(zH!=null||(zH=new wH(sH,tH)),e=xH(zH,b,c));d[b]=e},CH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},cw=function(a,b,c){var d=Rw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function DH(a,b){var c;if(!ch(a)||!dh(b))throw G(this.getName(),["Object","Object|undefined"],arguments);var d=yd(b)||{},e=yd(a,this.K,1).Ab(),f=e.D;d.omitEventContext&&(f=fq(new Vp(e.D.eventId,e.D.priorityId)));var g=new AH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=BH(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;S(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=CH(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var v=t.value;R(g,v,q[v])}g.isAborted=e.isAborted;c=zd(zw(g),this.K,1);return c}DH.M="internal.copyPreHit";function EH(a,b){var c=null;return zd(c,this.K,2)}EH.publicName="createArgumentsQueue";function FH(a){return zd(function(c){var d=WB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
WB(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}FH.M="internal.createGaCommandQueue";function GH(a){return zd(function(){if(!jb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
yh(iF(this).Kb())?2:1)}GH.publicName="createQueue";function HH(a,b){var c=null;if(!jh(a)||!kh(b))throw G(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new vd(new RegExp(a,d))}catch(e){}return c}HH.M="internal.createRegex";function IH(a){}IH.M="internal.declareConsentState";function JH(a){var b="";return b}JH.M="internal.decodeUrlHtmlEntities";function KH(a,b,c){var d;return d}KH.M="internal.decorateUrlWithGaCookies";function LH(){}LH.M="internal.deferCustomEvents";function MH(a){var b;I(this,"detect_user_provided_data","auto");var c=yd(a)||{},d=qx({Be:!!c.includeSelector,Ce:!!c.includeVisibility,Vf:c.excludeElementSelectors,Zb:c.fieldFilters,Jh:!!c.selectMultipleElements});b=new Ua;var e=new md;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(NH(f[g]));d.Fj!==void 0&&b.set("preferredEmailElement",NH(d.Fj));b.set("status",d.status);if(E(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(qc&&
qc.userAgent||"")){}return b}
var OH=function(a){switch(a){case ox.kc:return"email";case ox.zd:return"phone_number";case ox.sd:return"first_name";case ox.yd:return"last_name";case ox.Ri:return"street";case ox.Mh:return"city";case ox.Mi:return"region";case ox.Nf:return"postal_code";case ox.Je:return"country"}},NH=function(a){var b=new Ua;b.set("userData",a.la);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(E(33)){}else switch(a.type){case ox.kc:b.set("type","email")}return b};MH.M="internal.detectUserProvidedData";
function RH(a,b){return f}RH.M="internal.enableAutoEventOnClick";var UH=function(a){if(!SH){var b=function(){var c=z.body;if(c)if(TH)(new MutationObserver(function(){for(var e=0;e<SH.length;e++)Jc(SH[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Hc(c,"DOMNodeInserted",function(){d||(d=!0,Jc(function(){d=!1;for(var e=0;e<SH.length;e++)Jc(SH[e])}))})}};SH=[];z.body?b():Jc(b)}SH.push(a)},TH=!!x.MutationObserver,SH;
function ZH(a,b){return p}ZH.M="internal.enableAutoEventOnElementVisibility";function $H(){}$H.M="internal.enableAutoEventOnError";var aI={},bI=[],cI={},dI=0,eI=0;
var gI=function(){sb(cI,function(a,b){var c=aI[a];c&&sb(b,function(d,e){fI(e,c)})})},jI=function(a,b){var c=""+b;if(aI[c])aI[c].push(a);else{var d=[a];aI[c]=d;var e=cI[c];e||(e={},cI[c]=e);bI.push(function(f){var g=f.target;if(g){var h=uF(g);if(h){var m=hI(h,"gtmFormInteractId",function(){return dI++}),n=hI(g,"gtmFormInteractFieldId",function(){return eI++});if(m!==null&&n!==null){var p=e[m];p?(p.Gc&&(x.clearTimeout(p.Gc),p.fc.getAttribute("data-gtm-form-interact-field-id")!==n&&fI(p,d)),p.fc=g,iI(p,
d,b)):(e[m]={form:h,fc:g,sequenceNumber:0,Gc:null},iI(e[m],d,b))}}}})}},fI=function(a,b){var c=a.form,d=a.fc,e=qF(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
tF(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;ED(e);a.sequenceNumber++;a.Gc=null},iI=function(a,b,c){c?a.Gc=x.setTimeout(function(){fI(a,b)},c):fI(a,b)},hI=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function kI(a,b){var c=this;if(!dh(a))throw G(this.getName(),["Object|undefined","any"],arguments);eF([function(){I(c,"detect_form_interaction_events")}]);var d=kF(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(lF("fil","init",!1)){var f=lF("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else Hc(z,"change",function(g){for(var h=0;h<bI.length;h++)bI[h](g)}),Hc(x,"pagehide",function(){gI()}),
jI(d,e),mF("fil","reg",jI),mF("fil","init",!0);return d}kI.M="internal.enableAutoEventOnFormInteraction";
var lI=function(a,b,c,d,e){var f=lF("fsl",c?"nv.mwt":"mwt",0),g;g=c?lF("fsl","nv.ids",[]):lF("fsl","ids",[]);if(!g.length)return!0;var h=qF(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);M(121);if(m==="https://www.facebook.com/tr/")return M(122),!0;h["gtm.elementUrl"]=m;h["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(h["gtm.interactedFormName"]=a.getAttribute("name"));e&&(h["gtm.formSubmitElement"]=e,h["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!DD(h,FD(b,
f),f))return!1}else DD(h,function(){},f||2E3);return!0},mI=function(){var a=[],b=function(c){return ob(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},nI=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},oI=function(){var a=mI(),b=HTMLFormElement.prototype.submit;Hc(z,"click",function(c){var d=c.target;if(d){var e=Nc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&Kc(e,"value")){var f=uF(e);f&&a.store(f,e)}}},!1);Hc(z,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=nI(d)&&!e,g=a.get(d),h=!0;if(lI(d,function(){if(h){var m=null,n={};g&&(m=z.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),hc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
hc(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))h=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;lI(c,function(){d&&b.call(c)},!1,nI(c))&&(b.call(c),d=
!1)}};
function pI(a,b){var c=this;if(!dh(a))throw G(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");eF([function(){I(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=kF(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var h=function(n){return Math.max(g,n)};pF("fsl","mwt",h,0);e||pF("fsl","nv.mwt",h,0)}var m=function(n){n.push(f);
return n};pF("fsl","ids",m,[]);e||pF("fsl","nv.ids",m,[]);lF("fsl","init",!1)||(oI(),mF("fsl","init",!0));return f}pI.M="internal.enableAutoEventOnFormSubmit";
function uI(){var a=this;}uI.M="internal.enableAutoEventOnGaSend";var vI={},wI=[];
var yI=function(a,b){var c=""+b;if(vI[c])vI[c].push(a);else{var d=[a];vI[c]=d;var e=xI("gtm.historyChange-v2"),f=-1;wI.push(function(g){f>=0&&x.clearTimeout(f);b?f=x.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},xI=function(a){var b=x.location.href,c={source:null,state:x.history.state||null,url:Mk(Pk(b)),cb:Jk(Pk(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.cb!==d.cb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.cb,
"gtm.newUrlFragment":d.cb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;ED(h)}}},zI=function(a,b){var c=x.history,d=c[a];if(jb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=x.location.href;b({source:a,state:e,url:Mk(Pk(h)),cb:Jk(Pk(h),"fragment")})}}catch(e){}},BI=function(a){x.addEventListener("popstate",function(b){var c=AI(b);a({source:"popstate",state:b.state,url:Mk(Pk(c)),cb:Jk(Pk(c),
"fragment")})})},CI=function(a){x.addEventListener("hashchange",function(b){var c=AI(b);a({source:"hashchange",state:null,url:Mk(Pk(c)),cb:Jk(Pk(c),"fragment")})})},AI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||x.location.href};
function DI(a,b){var c=this;if(!dh(a))throw G(this.getName(),["Object|undefined","any"],arguments);eF([function(){I(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!lF(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<wI.length;n++)wI[n](m)},f=kF(b),yI(f,e),mF(d,"reg",yI)):g=xI("gtm.historyChange");CI(g);BI(g);zI("pushState",
g);zI("replaceState",g);mF(d,"init",!0)}else if(d==="ehl"){var h=lF(d,"reg");h&&(f=kF(b),h(f,e))}d==="hl"&&(f=void 0);return f}DI.M="internal.enableAutoEventOnHistoryChange";var EI=["http://","https://","javascript:","file://"];
var FI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Vc(b,"href");if(c.indexOf(":")!==-1&&!EI.some(function(h){return Eb(c,h)}))return!1;var d=c.indexOf("#"),e=Vc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=Mk(Pk(c)),g=Mk(Pk(x.location.href));return f!==g}return!0},GI=function(a,b){for(var c=Jk(Pk((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Vc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},HI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Nc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=lF("lcl",e?"nv.mwt":"mwt",0),g;g=e?lF("lcl","nv.ids",[]):lF("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=lF("lcl","aff.map",{})[n];p&&!GI(p,d)||h.push(n)}if(h.length){var q=FI(c,d),r=qF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Lc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!ob(String(Vc(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),v=x[(Vc(d,"target")||"_self").substring(1)],u=!0,w=FD(function(){var y;if(y=u&&v){var A;a:if(t){var C;try{C=new MouseEvent(c.type,{bubbles:!0})}catch(D){if(!z.createEvent){A=!1;break a}C=z.createEvent("MouseEvents");C.initEvent(c.type,!0,!0)}C.C=!0;c.target.dispatchEvent(C);A=!0}else A=!1;y=!A}y&&(v.location.href=Vc(d,
"href"))},f);if(DD(r,w,f))u=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else DD(r,function(){},f||2E3);return!0}}}var b=0;Hc(z,"click",a,!1);Hc(z,"auxclick",a,!1)};
function II(a,b){var c=this;if(!dh(a))throw G(this.getName(),["Object|undefined","any"],arguments);var d=yd(a);eF([function(){I(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=kF(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};pF("lcl","mwt",n,0);f||pF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};pF("lcl","ids",p,[]);f||pF("lcl","nv.ids",p,[]);g&&pF("lcl","aff.map",function(q){q[h]=g;return q},{});lF("lcl","init",!1)||(HI(),mF("lcl","init",!0));return h}II.M="internal.enableAutoEventOnLinkClick";var JI,KI;
var LI=function(a){return lF("sdl",a,{})},MI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];pF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},PI=function(){function a(){NI();OI(a,!0)}return a},QI=function(){function a(){f?e=x.setTimeout(a,c):(e=0,NI(),OI(b));f=!1}function b(){d&&JI();e?f=!0:(e=x.setTimeout(a,c),mF("sdl","pending",!0))}var c=250,d=!1;z.scrollingElement&&z.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
OI=function(a,b){lF("sdl","init",!1)&&!RI()&&(b?Ic(x,"scrollend",a):Ic(x,"scroll",a),Ic(x,"resize",a),mF("sdl","init",!1))},NI=function(){var a=JI(),b=a.depthX,c=a.depthY,d=b/KI.scrollWidth*100,e=c/KI.scrollHeight*100;SI(b,"horiz.pix","PIXELS","horizontal");SI(d,"horiz.pct","PERCENT","horizontal");SI(c,"vert.pix","PIXELS","vertical");SI(e,"vert.pct","PERCENT","vertical");mF("sdl","pending",!1)},SI=function(a,b,c,d){var e=LI(b),f={},g;for(g in e)if(f={Fe:f.Fe},f.Fe=g,e.hasOwnProperty(f.Fe)){var h=
Number(f.Fe);if(!(a<h)){var m={};ND((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.Fe].join(","),m));pF("sdl",b,function(n){return function(p){delete p[n.Fe];return p}}(f),{})}}},UI=function(){pF("sdl","scr",function(a){a||(a=z.scrollingElement||z.body&&z.body.parentNode);return KI=a},!1);pF("sdl","depth",function(a){a||(a=TI());return JI=a},!1)},TI=function(){var a=0,b=0;return function(){var c=Uw(),d=c.height;
a=Math.max(KI.scrollLeft+c.width,a);b=Math.max(KI.scrollTop+d,b);return{depthX:a,depthY:b}}},RI=function(){return!!(Object.keys(LI("horiz.pix")).length||Object.keys(LI("horiz.pct")).length||Object.keys(LI("vert.pix")).length||Object.keys(LI("vert.pct")).length)};
function VI(a,b){var c=this;if(!ch(a))throw G(this.getName(),["Object","any"],arguments);eF([function(){I(c,"detect_scroll_events")}]);UI();if(!KI)return;var d=kF(b),e=yd(a);switch(e.horizontalThresholdUnits){case "PIXELS":MI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":MI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":MI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":MI(e.verticalThresholds,
d,"vert.pct")}lF("sdl","init",!1)?lF("sdl","pending",!1)||Jc(function(){NI()}):(mF("sdl","init",!0),mF("sdl","pending",!0),Jc(function(){NI();if(RI()){var f=QI();"onscrollend"in x?(f=PI(),Hc(x,"scrollend",f)):Hc(x,"scroll",f);Hc(x,"resize",f)}else mF("sdl","init",!1)}));return d}VI.M="internal.enableAutoEventOnScroll";function WI(a){return function(){if(a.limit&&a.Aj>=a.limit)a.Dh&&x.clearInterval(a.Dh);else{a.Aj++;var b=zb();ED({event:a.eventName,"gtm.timerId":a.Dh,"gtm.timerEventNumber":a.Aj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Lm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Lm,"gtm.triggers":a.Iq})}}}
function XI(a,b){
return f}XI.M="internal.enableAutoEventOnTimer";
var YI=function(a,b,c){function d(){var g=a();f+=e?(zb()-e)*g.playbackRate/1E3:0;e=zb()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.ej,q=m?Math.round(m):h?Math.round(n.ej*h):Math.round(n.dm),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=z.hidden?!1:Vw(c)>=.5;d();var v=void 0;b!==void 0&&(v=[b]);var u=qF(c,"gtm.video",v);u["gtm.videoProvider"]="youtube";u["gtm.videoStatus"]=g;u["gtm.videoUrl"]=n.url;u["gtm.videoTitle"]=n.title;u["gtm.videoDuration"]=Math.round(p);u["gtm.videoCurrentTime"]=
Math.round(q);u["gtm.videoElapsedTime"]=Math.round(f);u["gtm.videoPercent"]=r;u["gtm.videoVisible"]=t;return u},Hm:function(){e=zb()},ve:function(){d()}}};var kc=va(["data-gtm-yt-inspected-"]),ZI=["www.youtube.com","www.youtube-nocookie.com"],$I,aJ=!1;
var bJ=function(a,b,c){var d=a.map(function(g){return{ab:g,pg:g,ng:void 0}});if(!b.length)return d;var e=b.map(function(g){return{ab:g*c,pg:void 0,ng:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.ab-h.ab});return f},cJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},dJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},eJ=function(a,b){var c,d;function e(){t=YI(function(){return{url:w,title:y,ej:u,dm:a.getCurrentTime(),playbackRate:A}},b.hc,a.getIframe());u=0;y=w="";A=1;return f}function f(H){switch(H){case 1:u=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var L=a.getVideoData();y=L?L.title:""}A=a.getPlaybackRate();if(b.Zi){var Q=t.createEvent("start");ED(Q)}else t.ve();v=bJ(b.Hj,b.Gj,a.getDuration());return g(H);default:return f}}function g(){C=a.getCurrentTime();D=yb().getTime();
t.Hm();r();return h}function h(H){var L;switch(H){case 0:return n(H);case 2:L="pause";case 3:var Q=a.getCurrentTime()-C;L=Math.abs((yb().getTime()-D)/1E3*A-Q)>1?"seek":L||"buffering";if(a.getCurrentTime())if(b.Yi){var ca=t.createEvent(L);ED(ca)}else t.ve();q();return m;case -1:return e(H);default:return h}}function m(H){switch(H){case 0:return n(H);case 1:return g(H);case -1:return e(H);default:return m}}function n(){for(;d;){var H=c;x.clearTimeout(d);H()}if(b.Xi){var L=t.createEvent("complete",1);
ED(L)}return e(-1)}function p(){}function q(){d&&(x.clearTimeout(d),d=0,c=p)}function r(){if(v.length&&A!==0){var H=-1,L;do{L=v[0];if(L.ab>a.getDuration())return;H=(L.ab-a.getCurrentTime())/A;if(H<0&&(v.shift(),v.length===0))return}while(H<0);c=function(){d=0;c=p;if(v.length>0&&v[0].ab===L.ab){v.shift();var Q=t.createEvent("progress",L.ng,L.pg);ED(Q)}r()};d=x.setTimeout(c,H*1E3)}}var t,v=[],u,w,y,A,C,D,F=e(-1);d=0;c=p;return{onStateChange:function(H){F=F(H)},onPlaybackRateChange:function(H){C=a.getCurrentTime();
D=yb().getTime();t.ve();A=H;q();r()}}},gJ=function(a){Jc(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)fJ(d[f],a)}var c=z;b();UH(b)})},fJ=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.hc)&&(mc(a,"data-gtm-yt-inspected-"+b.hc),hJ(a,b.Yf))){a.id||(a.id=iJ());var c=x.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=eJ(d,b),f={},g;for(g in e)f={jg:f.jg},f.jg=g,e.hasOwnProperty(f.jg)&&d.addEventListener(f.jg,function(h){return function(m){return e[h.jg](m.data)}}(f))}},
hJ=function(a,b){var c=a.getAttribute("src");if(jJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":($I||($I=z.location.protocol+"//"+z.location.hostname,z.location.port&&($I+=":"+z.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent($I));var f;f=Tb(d);a.src=Ub(f).toString();return!0}}return!1},jJ=function(a,b){if(!a)return!1;for(var c=0;c<ZI.length;c++)if(a.indexOf("//"+ZI[c]+"/"+b)>=0)return!0;
return!1},iJ=function(){var a=""+Math.round(Math.random()*1E9);return z.getElementById(a)?iJ():a};
function kJ(a,b){var c=this;var d=function(){gJ(q)};if(!ch(a))throw G(this.getName(),["Object","any"],arguments);eF([function(){I(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=kF(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=dJ(yd(a.get("progressThresholdsPercent"))),n=cJ(yd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={Zi:f,Xi:g,Yi:h,Gj:m,Hj:n,Yf:p,hc:e},r=x.YT;if(r)return r.ready&&r.ready(d),e;var t=x.onYouTubeIframeAPIReady;x.onYouTubeIframeAPIReady=function(){t&&t();d()};Jc(function(){for(var v=z.getElementsByTagName("script"),u=v.length,w=0;w<u;w++){var y=v[w].getAttribute("src");if(jJ(y,"iframe_api")||jJ(y,"player_api"))return e}for(var A=z.getElementsByTagName("iframe"),C=A.length,D=0;D<C;D++)if(!aJ&&hJ(A[D],q.Yf))return Cc("https://www.youtube.com/iframe_api"),
aJ=!0,e});return e}kJ.M="internal.enableAutoEventOnYouTubeActivity";aJ=!1;function lJ(a,b){if(!jh(a)||!dh(b))throw G(this.getName(),["string","Object|undefined"],arguments);var c=b?yd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Fh(f,c);return e}lJ.M="internal.evaluateBooleanExpression";var mJ;function nJ(a){var b=!1;return b}nJ.M="internal.evaluateMatchingRules";function XJ(){return mr(7)&&mr(9)&&mr(10)};function SK(a,b,c,d){}SK.M="internal.executeEventProcessor";function TK(a){var b;return zd(b,this.K,1)}TK.M="internal.executeJavascriptString";function UK(a){var b;return b};function VK(a){var b="";return b}VK.M="internal.generateClientId";function WK(a){var b={};return zd(b)}WK.M="internal.getAdsCookieWritingOptions";function XK(a,b){var c=!1;return c}XK.M="internal.getAllowAdPersonalization";function YK(){var a;return a}YK.M="internal.getAndResetEventUsage";function ZK(a,b){b=b===void 0?!0:b;var c;return c}ZK.M="internal.getAuid";var $K=null;
function aL(){var a=new Ua;I(this,"read_container_data"),E(49)&&$K?a=$K:(a.set("containerId",'G-15FN2QGQ72'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",mg),a.set("previewMode",ng.Pm),a.set("environmentMode",ng.jp),a.set("firstPartyServing",jk()||Kj.N),a.set("containerUrl",tc),a.Ta(),E(49)&&($K=a));return a}
aL.publicName="getContainerVersion";function bL(a,b){b=b===void 0?!0:b;var c;return c}bL.publicName="getCookieValues";function cL(){var a="";return a}cL.M="internal.getCorePlatformServicesParam";function dL(){return fo()}dL.M="internal.getCountryCode";function eL(){var a=[];a=vm();return zd(a)}eL.M="internal.getDestinationIds";function fL(a){var b=new Ua;return b}fL.M="internal.getDeveloperIds";function gL(a){var b;return b}gL.M="internal.getEcsidCookieValue";function hL(a,b){var c=null;return c}hL.M="internal.getElementAttribute";function iL(a){var b=null;return b}iL.M="internal.getElementById";function jL(a){var b="";return b}jL.M="internal.getElementInnerText";function kL(a,b){var c=null;return zd(c)}kL.M="internal.getElementProperty";function lL(a){var b;return b}lL.M="internal.getElementValue";function mL(a){var b=0;return b}mL.M="internal.getElementVisibilityRatio";function nL(a){var b=null;return b}nL.M="internal.getElementsByCssSelector";
function oL(a){var b;if(!jh(a))throw G(this.getName(),["string"],arguments);I(this,"read_event_data",a);var c;a:{var d=a,e=iF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var v=r[t].split("."),u=0;u<v.length;u++)n.push(v[u]),u!==v.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),C=A.next();!C.done;C=
A.next()){var D=C.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var F=l(w),H=F.next();!H.done;H=F.next()){if(f==null){c=void 0;break a}f=f[H.value]}c=f}else c=void 0}b=zd(c,this.K,1);return b}oL.M="internal.getEventData";var pL={};pL.enableDecodeUri=E(92);pL.enableGaAdsConversions=E(122);pL.enableGaAdsConversionsClientId=E(121);pL.enableOverrideAdsCps=E(170);pL.enableUrlDecodeEventUsage=E(139);function qL(){return zd(pL)}qL.M="internal.getFlags";function rL(){var a;return a}rL.M="internal.getGsaExperimentId";function sL(){return new vd(sE)}sL.M="internal.getHtmlId";function tL(a){var b;return b}tL.M="internal.getIframingState";function uL(a,b){var c={};return zd(c)}uL.M="internal.getLinkerValueFromLocation";function vL(){var a=new Ua;return a}vL.M="internal.getPrivacyStrings";function wL(a,b){var c;if(!jh(a)||!jh(b))throw G(this.getName(),["string","string"],arguments);var d=Rw(a)||{};c=zd(d[b],this.K);return c}wL.M="internal.getProductSettingsParameter";function xL(a,b){var c;if(!jh(a)||!nh(b))throw G(this.getName(),["string","boolean|undefined"],arguments);I(this,"get_url","query",a);var d=Jk(Pk(x.location.href),"query"),e=Gk(d,a,b);c=zd(e,this.K);return c}xL.publicName="getQueryParameters";function yL(a,b){var c;return c}yL.publicName="getReferrerQueryParameters";function zL(a){var b="";return b}zL.publicName="getReferrerUrl";function AL(){return go()}AL.M="internal.getRegionCode";function BL(a,b){var c;if(!jh(a)||!jh(b))throw G(this.getName(),["string","string"],arguments);var d=Fq(a);c=zd(d[b],this.K);return c}BL.M="internal.getRemoteConfigParameter";function CL(){var a=new Ua;a.set("width",0);a.set("height",0);return a}CL.M="internal.getScreenDimensions";function DL(){var a="";return a}DL.M="internal.getTopSameDomainUrl";function EL(){var a="";return a}EL.M="internal.getTopWindowUrl";function FL(a){var b="";if(!kh(a))throw G(this.getName(),["string|undefined"],arguments);I(this,"get_url",a);b=Jk(Pk(x.location.href),a);return b}FL.publicName="getUrl";function GL(){I(this,"get_user_agent");return qc.userAgent}GL.M="internal.getUserAgent";function HL(){var a;return a?zd(My(a)):a}HL.M="internal.getUserAgentClientHints";var JL=function(a){var b=a.eventName===J.m.bd&&Xm()&&dy(a),c=P(a,O.A.yl),d=P(a,O.A.Uj),e=P(a,O.A.Hf),f=P(a,O.A.ne),g=P(a,O.A.xg),h=P(a,O.A.Od),m=P(a,O.A.yg),n=P(a,O.A.zg),p=!!cy(a)||!!P(a,O.A.Sh);return!(!Tc()&&qc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&IL)},IL=!1;
var KL=function(a){var b=0,c=0;return{start:function(){b=zb()},stop:function(){c=this.get()},get:function(){var d=0;a.rj()&&(d=zb()-b);return d+c}}},LL=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.N=!1;this.R=this.P=void 0};k=LL.prototype;k.no=function(a){var b=this;if(!this.C){this.N=z.hasFocus();this.isVisible=!z.hidden;this.isActive=!0;var c=function(d,e,f){Hc(d,e,function(g){b.C.stop();f(g);b.rj()&&b.C.start()})};c(x,"focus",function(){b.N=!0});c(x,"blur",function(){b.N=
!1});c(x,"pageshow",function(d){b.isActive=!0;d.persisted&&M(56);b.R&&b.R()});c(x,"pagehide",function(){b.isActive=!1;b.P&&b.P()});c(z,"visibilitychange",function(){b.isVisible=!z.hidden});dy(a)&&!wc()&&c(x,"beforeunload",function(){IL=!0});this.Kj(!0);this.H=0}};k.Kj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.Bh(),this.C=KL(this),this.rj()&&this.C.start()};k.Hq=function(a){var b=this.Bh();b>0&&S(a,J.m.Lg,b)};k.Gp=function(a){S(a,J.m.Lg);this.Kj();this.H=0};k.rj=function(){return this.N&&
this.isVisible&&this.isActive};k.wp=function(){return this.H+this.Bh()};k.Bh=function(){return this.C&&this.C.get()||0};k.nq=function(a){this.P=a};k.Fm=function(a){this.R=a};var ML=function(a){db("GA4_EVENT",a)};var NL=function(a){var b=P(a,O.A.ml);if(Array.isArray(b))for(var c=0;c<b.length;c++)ML(b[c]);var d=gb("GA4_EVENT");d&&S(a,"_eu",d)},OL=function(){delete cb.GA4_EVENT};function PL(){return x.gaGlobal=x.gaGlobal||{}}function QL(){var a=PL();a.hid=a.hid||pb();return a.hid}function RL(a,b){var c=PL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var SL=["GA1"];
var TL=function(a,b,c){var d=P(a,O.A.Wj);if(d===void 0||c<=d)S(a,J.m.Rb,b),R(a,O.A.Wj,c)},VL=function(a,b){var c=Jv(a,J.m.Rb);if(N(a.D,J.m.Nc)&&N(a.D,J.m.Mc)||b&&c===b)return c;if(c){c=""+c;if(!UL(c,a))return M(31),a.isAborted=!0,"";RL(c,hp(J.m.ia));return c}M(32);a.isAborted=!0;return""},WL=function(a){var b=P(a,O.A.ya),c=b.prefix+"_ga",d=ys(b.prefix+"_ga",b.domain,b.path,SL,J.m.ia);if(!d){var e=String(N(a.D,J.m.gd,""));e&&e!==c&&(d=ys(e,b.domain,b.path,SL,J.m.ia))}return d},UL=function(a,b){var c;
var d=P(b,O.A.ya),e=d.prefix+"_ga",f=Kr(d,void 0,void 0,J.m.ia);if(N(b.D,J.m.Jc)===!1&&WL(b)===a)c=!0;else{var g;g=[SL[0],vs(d.domain,d.path),a].join(".");c=qs(e,g,f)!==1}return c};
var XL=function(a){if(a){var b;a:{var c=(Eb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=zt(c,2);break a}catch(d){}b=void 0}return b}},ZL=function(a,b){var c;a:{var d=YL,e=xt[2];if(e){var f,g=ts(b.domain),h=us(b.path),m=Object.keys(e.Kh),n=Ct.get(2),p;if(f=(p=is(a,g,h,m,n))==null?void 0:p.To){var q=zt(f,2,d);c=q?Et(q):void 0;break a}}c=void 0}if(c){var r=Dt(a,2,YL);if(r&&r.length>1){ML(28);var t;if(r&&r.length!==0){for(var v,u=-Infinity,w=l(r),y=w.next();!y.done;y=w.next()){var A=y.value;
if(A.t!==void 0){var C=Number(A.t);!isNaN(C)&&C>u&&(u=C,v=A)}}t=v}else t=void 0;var D=t;D&&D.t!==c.t&&(ML(32),c=D)}return Bt(c,2)}},YL=function(a){a&&(a==="GS1"?ML(33):a==="GS2"&&ML(34))},$L=function(a){var b=XL(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||ML(29);d||ML(30);isNaN(e)&&ML(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};

var bM=function(a,b,c){if(!b)return a;if(!a)return b;var d=$L(a);if(!d)return b;var e,f=ub((e=N(c.D,J.m.qf))!=null?e:30),g=P(c,O.A.hb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=$L(b);if(!h)return a;h.o=d.o+1;var m;return(m=aM(h))!=null?m:b},dM=function(a,b){var c=P(b,O.A.ya),d=cM(b,c),e=XL(a);if(!e)return!1;var f=Kr(c||{},void 0,void 0,Ct.get(2));qs(d,void 0,f);return Ft(d,e,2,c)!==1},eM=function(a){var b=P(a,O.A.ya);return ZL(cM(a,b),b)},fM=function(a){var b=P(a,O.A.hb),c={};c.s=Jv(a,J.m.wc);
c.o=Jv(a,J.m.Yg);var d;d=Jv(a,J.m.Xg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=P(a,O.A.Jf),c.j=P(a,O.A.Kf)||0,c.l=!!P(a,J.m.fi),c.h=Jv(a,J.m.Mg),c);return aM(e)},aM=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=ub(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Bt(c,2)}},cM=function(a,b){return b.prefix+"_ga_"+a.target.ids[Ep[6]]};
var gM=function(a){var b=N(a.D,J.m.Pa),c=a.D.H[J.m.Pa];if(c===b)return c;var d=jd(b,null);c&&c[J.m.ma]&&(d[J.m.ma]=(d[J.m.ma]||[]).concat(c[J.m.ma]));return d},hM=function(a,b){var c=Rs(!0);return c._up!=="1"?{}:{clientId:c[a],ub:c[b]}},iM=function(a,b,c){var d=Rs(!0),e=d[b];e&&(TL(a,e,2),UL(e,a));var f=d[c];f&&dM(f,a);return{clientId:e,ub:f}},jM=function(){var a=Lk(x.location,"host"),b=Lk(Pk(z.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},kM=function(a){if(!N(a.D,
J.m.Eb))return{};var b=P(a,O.A.ya),c=b.prefix+"_ga",d=cM(a,b);Zs(function(){var e;if(hp("analytics_storage"))e={};else{var f={_up:"1"},g;g=Jv(a,J.m.Rb);e=(f[c]=g,f[d]=fM(a),f)}return e},1);return!hp("analytics_storage")&&jM()?hM(c,d):{}},mM=function(a){var b=gM(a)||{},c=P(a,O.A.ya),d=c.prefix+"_ga",e=cM(a,c),f={};at(b[J.m.de],!!b[J.m.ma])&&(f=iM(a,d,e),f.clientId&&f.ub&&(lM=!0));b[J.m.ma]&&Ys(function(){var g={},h=WL(a);h&&(g[d]=h);var m=eM(a);m&&(g[e]=m);var n=fs("FPLC",void 0,void 0,J.m.ia);n.length&&
(g._fplc=n[0]);return g},b[J.m.ma],b[J.m.Oc],!!b[J.m.vc]);return f},lM=!1;var nM=function(a){if(!P(a,O.A.xd)&&Xk(a.D)){var b=gM(a)||{},c=(at(b[J.m.de],!!b[J.m.ma])?Rs(!0)._fplc:void 0)||(fs("FPLC",void 0,void 0,J.m.ia).length>0?void 0:"0");S(a,"_fplc",c)}};function oM(a){(dy(a)||jk())&&S(a,J.m.bl,go()||fo());!dy(a)&&jk()&&S(a,J.m.sl,"::")}function pM(a){if(jk()&&!dy(a)){var b=E(176);E(187)&&E(201)&&(b=b&&!jo());b&&S(a,J.m.Pk,!0);if(E(78)){Xv(a);Yv(a,zp.Df.dn,Do(N(a.D,J.m.eb)));var c=zp.Df.fn;var d=N(a.D,J.m.Jc);Yv(a,c,d===!0?1:d===!1?0:void 0);Yv(a,zp.Df.bn,Do(N(a.D,J.m.yb)));Yv(a,zp.Df.Ym,vs(Co(N(a.D,J.m.ob)),Co(N(a.D,J.m.Tb))))}}};var rM=function(a,b){sp("grl",function(){return qM()})(b)||(M(35),a.isAborted=!0)},qM=function(){var a=zb(),b=a+864E5,c=20,d=5E3;return function(e){var f=zb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Yo=d,e.No=c);return g}};
var sM=function(a){var b=Jv(a,J.m.Va);return Jk(Pk(b),"host",!0)},tM=function(a){if(N(a.D,J.m.kf)!==void 0)a.copyToHitData(J.m.kf);else{var b=N(a.D,J.m.ki),c,d;a:{if(lM){var e=gM(a)||{};if(e&&e[J.m.ma])for(var f=sM(a),g=e[J.m.ma],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=sM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(S(a,J.m.kf,"1"),
ML(4))}};
var uM=function(a,b){tr()&&(a.gcs=ur(),P(b,O.A.Gf)&&(a.gcu="1"));a.gcd=yr(b.D);E(97)?a.npa=P(b,O.A.Lh)?"0":"1":sr(b.D)?a.npa="0":a.npa="1";Dr()&&(a._ng="1")},vM=function(a){return hp(J.m.U)&&hp(J.m.ia)?jk()&&P(a,O.A.Hi):!1},wM=function(a){if(P(a,O.A.xd))return{url:Yk("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=Uk(Xk(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=ey(a),d=N(a.D,J.m.Pb),e=c&&!ho()&&d!==!1&&XJ()&&hp(J.m.U)&&hp(J.m.ia)?17:16;return{url:Iz(e),
endpoint:e}},xM={};xM[J.m.Rb]="cid";xM[J.m.Uh]="gcut";xM[J.m.fd]="are";xM[J.m.Jg]="pscdl";xM[J.m.gi]="_fid";xM[J.m.Lk]="_geo";xM[J.m.Vb]="gdid";xM[J.m.be]="_ng";xM[J.m.Lc]="frm";xM[J.m.kf]="ir";xM[J.m.Pk]="fp";xM[J.m.zb]="ul";xM[J.m.Vg]="ni";xM[J.m.Wn]="pae";xM[J.m.Wg]="_rdi";xM[J.m.Pc]="sr";xM[J.m.ao]="tid";xM[J.m.ri]="tt";xM[J.m.yc]="ec_mode";xM[J.m.wl]="gtm_up";xM[J.m.uf]=
"uaa";xM[J.m.vf]="uab";xM[J.m.wf]="uafvl";xM[J.m.xf]="uamb";xM[J.m.yf]="uam";xM[J.m.zf]="uap";xM[J.m.Af]="uapv";xM[J.m.Bf]="uaw";xM[J.m.bl]="ur";xM[J.m.sl]="_uip";xM[J.m.Vn]="_prs";xM[J.m.md]="lps";xM[J.m.Ud]="gclgs";xM[J.m.Wd]="gclst";xM[J.m.Vd]="gcllp";var yM={};yM[J.m.Re]="cc";
yM[J.m.Se]="ci";yM[J.m.Te]="cm";yM[J.m.Ue]="cn";yM[J.m.We]="cs";yM[J.m.Xe]="ck";yM[J.m.Ua]="cu";yM[J.m.jf]="_tu";yM[J.m.Aa]="dl";yM[J.m.Va]="dr";yM[J.m.Db]="dt";yM[J.m.Xg]="seg";yM[J.m.wc]="sid";yM[J.m.Yg]="sct";yM[J.m.Qa]="uid";E(145)&&(yM[J.m.nf]="dp");var zM={};zM[J.m.Lg]="_et";zM[J.m.Ub]="edid";E(94)&&(zM._eu="_eu");var AM={};AM[J.m.Re]="cc";AM[J.m.Se]="ci";
AM[J.m.Te]="cm";AM[J.m.Ue]="cn";AM[J.m.We]="cs";AM[J.m.Xe]="ck";var BM={},CM=(BM[J.m.fb]=1,BM),DM=function(a,b,c){function d(T,Z){if(Z!==void 0&&!no.hasOwnProperty(T)){Z===null&&(Z="");var Y;var V=Z;T!==J.m.Mg?Y=!1:P(a,O.A.ie)||dy(a)?(e.ecid=V,Y=!0):Y=void 0;if(!Y&&T!==J.m.fi){var ka=Z;Z===!0&&(ka="1");Z===!1&&(ka="0");ka=String(ka);var ia;if(xM[T])ia=xM[T],e[ia]=ka;else if(yM[T])ia=yM[T],g[ia]=ka;else if(zM[T])ia=zM[T],f[ia]=ka;else if(T.charAt(0)==="_")e[T]=ka;else{var la;AM[T]?la=!0:T!==J.m.Ve?
la=!1:(typeof Z!=="object"&&C(T,Z),la=!0);la||C(T,Z)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=Ir({Ma:P(a,O.A.ib)});e._p=E(159)?ck:QL();if(c&&(c.Za||c.nj)&&(E(125)||(e.em=c.Lb),c.Ib)){var h=c.Ib.ye;h&&!E(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}P(a,O.A.Od)&&(e._gaz=1);uM(e,a);Br()&&(e.dma_cps=zr());e.dma=Ar();Xq(er())&&(e.tcfd=Cr());Jz()&&(e.tag_exp=Jz());Kz()&&(e.ptag_exp=Kz());var m=Jv(a,J.m.Vb);m&&(e.gdid=m);f.en=String(a.eventName);if(P(a,O.A.If)){var n=P(a,O.A.vl);f._fv=
n?2:1}P(a,O.A.ih)&&(f._nsi=1);if(P(a,O.A.ne)){var p=P(a,O.A.xl);f._ss=p?2:1}P(a,O.A.Hf)&&(f._c=1);P(a,O.A.wd)&&(f._ee=1);if(P(a,O.A.tl)){var q=Jv(a,J.m.sa)||N(a.D,J.m.sa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=rg(q[r])}var t=Jv(a,J.m.Ub);t&&(f.edid=t);var v=Jv(a,J.m.uc);if(v&&typeof v==="object")for(var u=l(Object.keys(v)),w=u.next();!w.done;w=u.next()){var y=w.value,A=v[y];A!==void 0&&(A===null&&(A=""),f["gap."+y]=String(A))}for(var C=function(T,Z){if(typeof Z!=="object"||
!CM[T]){var Y="ep."+T,V="epn."+T;T=mb(Z)?V:Y;var ka=mb(Z)?Y:V;f.hasOwnProperty(ka)&&delete f[ka];f[T]=String(Z)}},D=l(Object.keys(a.C)),F=D.next();!F.done;F=D.next()){var H=F.value;d(H,Jv(a,H))}(function(T){dy(a)&&typeof T==="object"&&sb(T||{},function(Z,Y){typeof Y!=="object"&&(e["sst."+Z]=String(Y))})})(Jv(a,J.m.Oi));Lz(e,Jv(a,J.m.ud));var L=Jv(a,J.m.Xb)||{};N(a.D,J.m.Pb,void 0,4)===!1&&(e.ngs="1");sb(L,function(T,Z){Z!==void 0&&((Z===null&&(Z=""),T!==J.m.Qa||g.uid)?b[T]!==Z&&(f[(mb(Z)?"upn.":"up.")+
String(T)]=String(Z),b[T]=Z):g.uid=String(Z))});if(E(176)){var Q=E(187)&&jo();if(jk()&&!Q){var ca=P(a,O.A.Jf);ca?e._gsid=ca:e.njid="1"}}else if(vM(a)){var U=P(a,O.A.Jf);U?e._gsid=U:e.njid="1"}var qa=wM(a);Eg.call(this,{ra:e,Md:g,ij:f},qa.url,qa.endpoint,dy(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};ra(DM,Eg);
var EM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},FM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;if(E(186)){var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e}return b},GM=function(a,b,c,d,e){var f=0,g=new x.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;
BA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},IM=function(a,b,c){var d;return d=EA(DA(new CA(function(e,f){var g=EM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");dm(a,g,void 0,GA(d,f),h)}),function(e,f){var g=EM(e,b),h=f.dedupe_key;h&&im(a,g,h)}),function(e,
f){var g=EM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?HM(a,g,void 0,d,h,GA(d,f)):em(a,g,void 0,h,void 0,GA(d,f))})},JM=function(a,b,c,d,e){Yl(a,2,b);var f=IM(a,d,e);HM(a,b,c,f)},HM=function(a,b,c,d,e,f){Tc()?AA(a,b,c,d,e,void 0,f):GM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},KM=function(a,b,c){var d=Pk(b),e=FM(d),f=IA(d);!E(132)||vc("; wv")||
vc("FBAN")||vc("FBAV")||xc()?JM(a,f,c,e):Ey(f,c,e,function(g){JM(a,f,c,e,g)})};var LM={AW:mn.Z.Um,G:mn.Z.fo,DC:mn.Z.co};function MM(a){var b=Zi(a);return""+Yr(b.map(function(c){return c.value}).join("!"))}function NM(a){var b=Cp(a);return b&&LM[b.prefix]}function OM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};
var PM=function(a,b,c,d){var e=a+"?"+b;d?cm(c,e,d):bm(c,e)},RM=function(a,b,c,d,e){var f=b,g=Wc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;QM&&(d=!Eb(h,Hz())&&!Eb(h,Gz()));if(d&&!IL)KM(e,h,c);else{var m=b;Tc()?em(e,a+"?"+m,c,{Hh:!0})||PM(a,m,e,c):PM(a,m,e,c)}},SM=function(a,b){function c(w){q.push(w+"="+encodeURIComponent(""+a.ra[w]))}var d=b.uq,e=b.yq,f=b.xq,g=b.wq,h=b.yp,m=b.Rp,n=b.Qp,p=b.op;if(d||e||f||g){var q=[];a.ra._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Md.uid&&
!n&&q.push("uid="+encodeURIComponent(""+a.Md.uid));c("dma");a.ra.dma_cps!=null&&c("dma_cps");a.ra.gcs!=null&&c("gcs");c("gcd");a.ra.npa!=null&&c("npa");a.ra.frm!=null&&c("frm");d&&(Jz()&&q.push("tag_exp="+Jz()),Kz()&&q.push("ptag_exp="+Kz()),PM("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),Uo({targetId:String(a.ra.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),
parameterEncoding:2,endpoint:19},Ya:b.Ya}));if(e&&(Jz()&&q.push("tag_exp="+Jz()),Kz()&&q.push("ptag_exp="+Kz()),q.push("z="+pb()),!m)){var r=h&&Eb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(r){var t=r+q.join("&");dm({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);Uo({targetId:String(a.ra.tid),request:{url:t,parameterEncoding:2,endpoint:47},Ya:b.Ya})}}if(f){var v="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");q=[];c("_gsid");c("gtm");E(176)&&a.ra._geo&&c("_geo");PM(v,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});Uo({targetId:String(a.ra.tid),request:{url:v+"?"+q.join("&"),parameterEncoding:2,endpoint:18},Ya:b.Ya})}if(g){var u="https://{ga4CollectionSubdomain.}google-analytics.com/g/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");q=[];q.push("v=2");q.push("t=g");c("_gsid");c("gtm");a.ra._geo&&c("_geo");PM(u,q.join("&"),{destinationId:a.destinationId||
"",endpoint:16,eventId:a.eventId,priorityId:a.priorityId});Uo({targetId:String(a.ra.tid),request:{url:u+"?"+q.join("&"),parameterEncoding:2,endpoint:16},Ya:b.Ya})}}},QM=!1;var TM=function(){this.N=1;this.P={};this.H=-1;this.C=new xg};k=TM.prototype;k.Nb=function(a,b){var c=this,d=new DM(a,this.P,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=JL(a),g,
h;f&&this.C.R(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=x.setTimeout,p;dy(a)?UM?(UM=!1,p=VM):p=WM:p=5E3;this.H=n.call(x,function(){c.flush()},p)}}else{var q=Ag(d,this.N++),r=q.params,t=q.body;g=r;h=t;RM(d.baseUrl,r,t,d.N,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var v=P(a,O.A.xg),u=P(a,O.A.Od),w=P(a,O.A.zg),y=P(a,O.A.yg),A=N(a.D,J.m.nb)!==!1,C=sr(a.D),D={uq:v,yq:u,xq:w,wq:y,yp:lo(),zr:A,yr:C,Rp:ho(),Qp:P(a,O.A.ie),
Ya:e,D:a.D,op:jo()};SM(d,D)}pA(a.D.eventId);Vo(function(){if(m){var F=Ag(d),H=F.body;g=F.params;h=H}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},Ya:e,isBatched:!1}})};k.add=function(a){if(E(100)){var b=P(a,O.A.Sh);if(b){S(a,J.m.yc,P(a,O.A.Vl));S(a,J.m.Vg,"1");this.Nb(a,b);return}}var c=cy(a);if(E(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=NM(e);if(h){var m=MM(g);f=(qn(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:
void 0;if(d&&d+6E4>zb())c=void 0,S(a,J.m.yc);else{var p=c,q=a.target.destinationId,r=NM(q);if(r){var t=MM(p),v=qn(r)||{},u=v[t];if(u)u.timestamp=zb(),u.sentTo=u.sentTo||{},u.sentTo[q]=zb(),u.pending=!0;else{var w={};v[t]={pending:!0,timestamp:zb(),sentTo:(w[q]=zb(),w)}}OM(v,t);pn(r,v)}}}!c||IL||E(125)&&!E(93)?this.Nb(a):this.zq(a)};k.flush=function(){if(this.C.events.length){var a=Cg(this.C,this.N++);RM(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",endpoint:this.C.endpoint,
eventId:this.C.ba,priorityId:this.C.ka});this.C=new xg;this.H>=0&&(x.clearTimeout(this.H),this.H=-1)}};k.gm=function(a,b){var c=Jv(a,J.m.yc);S(a,J.m.yc);b.then(function(d){var e={},f=(e[O.A.Sh]=d,e[O.A.Vl]=c,e),g=Hw(a.target.destinationId,J.m.Td,a.D.C);Kw(g,a.D.eventId,{eventMetadata:f})})};k.zq=function(a){var b=this,c=cy(a);if(xj(c)){var d=mj(c,E(93));d?E(100)?(this.gm(a,d),this.Nb(a)):d.then(function(g){b.Nb(a,g)},function(){b.Nb(a)}):this.Nb(a)}else{var e=wj(c);if(E(93)){var f=hj(e);f?E(100)?
(this.gm(a,f),this.Nb(a)):f.then(function(g){b.Nb(a,g)},function(){b.Nb(a,e)}):this.Nb(a,e)}else this.Nb(a,e)}};var VM=sg('',500),WM=sg('',5E3),UM=!0;
var XM=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;XM(a+"."+f,b[f],c)}else c[a]=b;return c},YM=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!hp(e)}return b},$M=function(a,b){var c=ZM.filter(function(e){return!hp(e)});if(c.length){var d=YM(c);ip(c,function(){for(var e=YM(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){R(b,O.A.Gf,!0);var n=f.map(function(p){return xo[p]}).join(".");n&&ay(b,"gcut",n);a(b)}})}},aN=function(a){dy(a)&&ay(a,"navt",Xc())},bN=function(a){dy(a)&&ay(a,"lpc",Mt())},cN=function(a){if(E(152)&&dy(a)){var b=N(a.D,J.m.Wb),c;b===!0&&(c="1");b===!1&&(c="0");c&&ay(a,"rdp",c)}},dN=function(a){E(147)&&dy(a)&&N(a.D,J.m.Qe,!0)===!1&&S(a,J.m.Qe,0)},eN=function(a,b){if(dy(b)){var c=P(b,O.A.Hf);(b.eventName==="page_view"||c)&&$M(a,b)}},fN=function(a){if(dy(a)&&a.eventName===J.m.Td&&
P(a,O.A.Gf)){var b=Jv(a,J.m.Uh);b&&(ay(a,"gcut",b),ay(a,"syn",1))}},gN=function(a){dy(a)&&R(a,O.A.Ha,!1)},hN=function(a){dy(a)&&(P(a,O.A.Ha)&&ay(a,"sp",1),P(a,O.A.lo)&&ay(a,"syn",1),P(a,O.A.Ke)&&(ay(a,"em_event",1),ay(a,"sp",1)))},iN=function(a){if(dy(a)){var b=ck;b&&ay(a,"tft",Number(b))}},jN=function(a){function b(e){var f=XM(J.m.fb,e);sb(f,function(g,h){S(a,g,h)})}if(dy(a)){var c=cw(a,"ccd_add_1p_data",!1)?1:0;ay(a,"ude",c);var d=N(a.D,J.m.fb);d!==void 0?(b(d),S(a,J.m.yc,"c")):b(P(a,O.A.jb));R(a,
O.A.jb)}},kN=function(a){if(dy(a)){var b=Gv();b&&ay(a,"us_privacy",b);var c=lr();c&&ay(a,"gdpr",c);var d=kr();d&&ay(a,"gdpr_consent",d);var e=tv.gppString;e&&ay(a,"gpp",e);var f=tv.C;f&&ay(a,"gpp_sid",f)}},lN=function(a){dy(a)&&Xm()&&N(a.D,J.m.za)&&ay(a,"adr",1)},mN=function(a){if(dy(a)){var b=Fz();b&&ay(a,"gcsub",b)}},nN=function(a){if(dy(a)){N(a.D,J.m.Pb,void 0,4)===!1&&ay(a,"ngs",1);ho()&&ay(a,"ga_rd",1);XJ()||ay(a,"ngst",1);var b=lo();b&&ay(a,"etld",b)}},oN=function(a){},pN=function(a){dy(a)&&Xm()&&ay(a,"rnd",gv())},ZM=[J.m.U,J.m.V];
var qN=function(a,b){var c;a:{var d=fM(a);if(d){if(dM(d,a)){c=d;break a}M(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:VL(a,b),ub:e}},rN=function(a,b,c,d,e){var f=Co(N(a.D,J.m.Rb));if(N(a.D,J.m.Nc)&&N(a.D,J.m.Mc))f?TL(a,f,1):(M(127),a.isAborted=!0);else{var g=f?1:8;R(a,O.A.ih,!1);f||(f=WL(a),g=3);f||(f=b,g=5);if(!f){var h=hp(J.m.ia),m=PL();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=xs(),g=7,R(a,O.A.If,!0),R(a,O.A.ih,!0));TL(a,f,g)}var n=P(a,O.A.hb),p=Math.floor(n/1E3),q=void 0;P(a,O.A.ih)||
(q=eM(a)||c);var r=ub(N(a.D,J.m.qf,30));r=Math.min(475,r);r=Math.max(5,r);var t=ub(N(a.D,J.m.mi,1E4)),v=$L(q);R(a,O.A.If,!1);R(a,O.A.ne,!1);R(a,O.A.Kf,0);v&&v.j&&R(a,O.A.Kf,Math.max(0,v.j-Math.max(0,p-v.t)));var u=!1;if(!v){R(a,O.A.If,!0);u=!0;var w={};v=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>v.t+r*60&&(u=!0,v.s=String(p),v.o++,v.g=!1,v.h=void 0);if(u)R(a,O.A.ne,!0),d.Gp(a);else if(d.wp()>t||a.eventName===J.m.bd)v.g=!0;P(a,O.A.ie)?N(a.D,J.m.Qa)?v.l=!0:(v.l&&!E(9)&&(v.h=void 0),v.l=
!1):v.l=!1;var y=v.h;if(P(a,O.A.ie)||dy(a)){var A=N(a.D,J.m.Mg),C=A?1:8;A||(A=y,C=4);A||(A=ws(),C=7);var D=A.toString(),F=C,H=P(a,O.A.kk);if(H===void 0||F<=H)S(a,J.m.Mg,D),R(a,O.A.kk,F)}e?(a.copyToHitData(J.m.wc,v.s),a.copyToHitData(J.m.Yg,v.o),a.copyToHitData(J.m.Xg,v.g?1:0)):(S(a,J.m.wc,v.s),S(a,J.m.Yg,v.o),S(a,J.m.Xg,v.g?1:0));R(a,J.m.fi,v.l?1:0);if(jk()){var L=x.crypto||x.msCrypto,Q;if(!(Q=v.d))a:{if(L&&L.getRandomValues)try{var ca=new Uint8Array(25);L.getRandomValues(ca);Q=btoa(String.fromCharCode.apply(String,
ta(ca))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(U){}Q=void 0}R(a,O.A.Jf,Q)}};var sN=window,tN=document,uN=function(a){var b=sN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||tN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&sN["ga-disable-"+a]===!0)return!0;try{var c=sN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(tN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return tN.getElementById("__gaOptOutExtension")?!0:!1};
var wN=function(a){return!a||vN.test(a)||po.hasOwnProperty(a)},xN=function(a){var b=J.m.Pc,c;c||(c=function(){});Jv(a,b)!==void 0&&S(a,b,c(Jv(a,b)))},yN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Ik(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},zN=function(a){N(a.D,J.m.Eb)&&(hp(J.m.ia)||N(a.D,J.m.Rb)||S(a,J.m.wl,!0));var b;var c;c=c===void 0?3:c;var d=x.location.href;if(d){var e=Pk(d).search.replace("?",""),f=Gk(e,"_gl",!1,!0)||"";b=f?Ss(f,c)!==void 0:!1}else b=!1;b&&dy(a)&&
ay(a,"glv",1);if(a.eventName!==J.m.qa)return{};N(a.D,J.m.Eb)&&Ku(["aw","dc"]);Mu(["aw","dc"]);var g=mM(a),h=kM(a);return Object.keys(g).length?g:h},AN=function(a){var b=Ib(a.D.getMergedValues(J.m.oa,1,Ao(Cq.C[J.m.oa])),".");b&&S(a,J.m.Vb,b);var c=Ib(a.D.getMergedValues(J.m.oa,2),".");c&&S(a,J.m.Ub,c)},BN={lp:""},CN={},DN=(CN[J.m.Re]=1,CN[J.m.Se]=1,CN[J.m.Te]=1,CN[J.m.Ue]=1,CN[J.m.We]=1,CN[J.m.Xe]=1,CN),vN=/^(_|ga_|google_|gtag\.|firebase_).*$/,EN=[bw,
Zv,Lv,dw,AN,Bw],FN=function(a){this.N=a;this.C=this.ub=this.clientId=void 0;this.ka=this.R=!1;this.Xa=0;this.P=!1;this.Ba=!0;this.ba=new TM;this.H=new LL};k=FN.prototype;k.kq=function(a,b,c){var d=this,e=Cp(this.N);if(e)if(c.eventMetadata[O.A.wd]&&a.charAt(0)==="_")c.onFailure();else{a!==J.m.qa&&a!==J.m.Cb&&wN(a)&&M(58);GN(c.C);var f=new AH(e,a,c);R(f,O.A.hb,b);var g=[J.m.ia],h=dy(f);R(f,O.A.jh,h);if(cw(f,J.m.ce,N(f.D,J.m.ce))||h)g.push(J.m.U),g.push(J.m.V);Oy(function(){kp(function(){d.lq(f)},g)});
E(88)&&a===J.m.qa&&cw(f,"ga4_ads_linked",!1)&&jn(ln(Lm.X.Da),function(){d.iq(a,c,f)})}else c.onFailure()};k.iq=function(a,b,c){function d(){for(var h=l(EN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}P(f,O.A.Ha)||f.isAborted||Rz(f)}var e=Cp(this.N),f=new AH(e,a,b);R(f,O.A.fa,K.J.Ga);R(f,O.A.Ha,!0);R(f,O.A.jh,P(c,O.A.jh));var g=[J.m.U,J.m.V];kp(function(){d();hp(g)||jp(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;R(f,O.A.ja,!0);R(f,O.A.He,m);R(f,O.A.Ie,n);
d()},g)},g)};k.lq=function(a){var b=this;try{bw(a);if(a.isAborted){OL();return}E(165)||(this.C=a);HN(a);IN(a);JN(a);KN(a);E(138)&&(a.isAborted=!0);Tv(a);var c={};rM(a,c);if(a.isAborted){a.D.onFailure();OL();return}E(165)&&(this.C=a);var d=c.No;c.Yo===0&&ML(25);d===0&&ML(26);dw(a);R(a,O.A.Qf,Lm.X.Hc);LN(a);MN(a);this.oo(a);this.H.Hq(a);NN(a);ON(a);PN(a);QN(a);this.Em(zN(a));var e=a.eventName===J.m.qa;e&&(this.P=!0);RN(a);e&&!a.isAborted&&this.Xa++>0&&ML(17);SN(a);TN(a);rN(a,this.clientId,this.ub,this.H,
!this.ka);UN(a);VN(a);WN(a);this.Ba=XN(a,this.Ba);YN(a);ZN(a);$N(a);aO(a);bO(a);nM(a);tM(a);pN(a);oN(a);nN(a);mN(a);lN(a);kN(a);iN(a);hN(a);fN(a);dN(a);cN(a);bN(a);aN(a);oM(a);pM(a);cO(a);dO(a);eO(a);Vv(a);Uv(a);aw(a);fO(a);gO(a);Bw(a);hO(a);jN(a);gN(a);iO(a);!this.P&&P(a,O.A.Ke)&&ML(18);NL(a);if(P(a,O.A.Ha)||a.isAborted){a.D.onFailure();OL();return}this.Em(qN(a,this.clientId));this.ka=!0;this.Eq(a);jO(a);eN(function(f){b.Wl(f)},a);this.H.Kj();kO(a);$v(a);if(a.isAborted){a.D.onFailure();OL();return}this.Wl(a);
a.D.onSuccess()}catch(f){a.D.onFailure()}OL()};k.Wl=function(a){this.ba.add(a)};k.Em=function(a){var b=a.clientId,c=a.ub;b&&c&&(this.clientId=b,this.ub=c)};k.flush=function(){this.ba.flush()};k.Eq=function(a){var b=this;if(!this.R){var c=hp(J.m.V),d=hp(J.m.ia);ip([J.m.V,J.m.ia],function(){var e=hp(J.m.V),f=hp(J.m.ia),g=!1,h={},m={};if(d!==f&&b.C&&b.ub&&b.clientId){var n=b.clientId,p;var q=$L(b.ub);p=q?q.h:void 0;if(f){var r=WL(b.C);if(r){b.clientId=r;var t=eM(b.C);t&&(b.ub=bM(t,b.ub,b.C))}else UL(b.clientId,
b.C),RL(b.clientId,!0);dM(b.ub,b.C);g=!0;h[J.m.ii]=n;E(69)&&p&&(h[J.m.Qn]=p)}else b.ub=void 0,b.clientId=void 0,x.gaGlobal={}}e&&!c&&(g=!0,m[O.A.Gf]=!0,h[J.m.Uh]=xo[J.m.V]);if(g){var v=Hw(b.N,J.m.Td,h);Kw(v,a.D.eventId,{eventMetadata:m})}d=f;c=e});this.R=!0}};k.oo=function(a){a.eventName!==J.m.Cb&&this.H.no(a)};var JN=function(a){var b=z.location.protocol;b!=="http:"&&b!=="https:"&&(M(29),a.isAborted=!0)},KN=function(a){qc&&qc.loadPurpose==="preview"&&(M(30),a.isAborted=!0)},LN=function(a){var b=
{prefix:String(N(a.D,J.m.eb,"")),path:String(N(a.D,J.m.Tb,"/")),flags:String(N(a.D,J.m.yb,"")),domain:String(N(a.D,J.m.ob,"auto")),Dc:Number(N(a.D,J.m.pb,63072E3))};R(a,O.A.ya,b)},NN=function(a){P(a,O.A.xd)?R(a,O.A.ie,!1):cw(a,"ccd_add_ec_stitching",!1)&&R(a,O.A.ie,!0)},ON=function(a){if(cw(a,"ccd_add_1p_data",!1)){var b=a.D.H[J.m.Zg];if(Bk(b)){var c=N(a.D,J.m.fb);if(c===null)R(a,O.A.ue,null);else if(b.enable_code&&id(c)&&R(a,O.A.ue,c),id(b.selectors)&&!P(a,O.A.rh)){var d={};R(a,O.A.rh,zk(b.selectors,
d));E(60)&&a.mergeHitDataForKey(J.m.uc,{ec_data_layer:wk(d)})}}}},PN=function(a){if(E(91)&&!E(88)&&cw(a,"ga4_ads_linked",!1)&&a.eventName===J.m.qa){var b=N(a.D,J.m.Oa)!==!1;if(b){var c=Hv(a);c.Dc&&(c.Dc=Math.min(c.Dc,7776E3));Iv({xe:b,De:Ao(N(a.D,J.m.Pa)),Ge:!!N(a.D,J.m.Eb),Tc:c})}}},QN=function(a){if(E(97)){var b=sr(a.D);N(a.D,J.m.Wb)===!0&&(b=!1);R(a,O.A.Lh,b)}},cO=function(a){if(!Ky(x))M(87);else if(Py!==void 0){M(85);var b=Iy();b?N(a.D,J.m.Wg)&&!dy(a)||Ny(b,a):M(86)}},RN=function(a){a.eventName===
J.m.qa&&(N(a.D,J.m.qb,!0)?(a.D.C[J.m.oa]&&(a.D.N[J.m.oa]=a.D.C[J.m.oa],a.D.C[J.m.oa]=void 0,S(a,J.m.oa)),a.eventName=J.m.bd):a.isAborted=!0)},MN=function(a){function b(c,d){no[c]||d===void 0||S(a,c,d)}sb(a.D.N,b);sb(a.D.C,b)},UN=function(a){var b=Up(a.D),c=function(d,e){DN[d]&&S(a,d,e)};id(b[J.m.Ve])?sb(b[J.m.Ve],function(d,e){c((J.m.Ve+"_"+d).toLowerCase(),e)}):sb(b,c)},SN=AN,jO=function(a){if(E(132)&&dy(a)&&!(vc("; wv")||vc("FBAN")||vc("FBAV")||xc())&&hp(J.m.ia)){R(a,O.A.yl,!0);dy(a)&&ay(a,"sw_exp",
1);a:{if(!E(132)||!dy(a))break a;var b=Uk(Xk(a.D),"/_/service_worker");By(b);}}},fO=function(a){if(a.eventName===J.m.Cb){var b=N(a.D,J.m.sc),c=N(a.D,J.m.Kc),d;d=Jv(a,b);c(d||N(a.D,b));a.isAborted=!0}},VN=function(a){if(!N(a.D,J.m.Mc)||!N(a.D,J.m.Nc)){var b=a.copyToHitData,c=J.m.Aa,d="",e=z.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),
m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Kb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,yN);var p=a.copyToHitData,q=J.m.Va,r;a:{var t=fs("_opt_expid",void 0,void 0,J.m.ia)[0];if(t){var v=Ik(t);if(v){var u=v.split("$");if(u.length===3){r=u[2];break a}}}var w=rp.ga4_referrer_override;if(w!==void 0)r=w;else{var y=pk("gtm.gtagReferrer."+a.target.destinationId),A=z.referrer;r=y?""+y:A}}p.call(a,q,r||void 0,yN);a.copyToHitData(J.m.Db,
z.title);a.copyToHitData(J.m.zb,(qc.language||"").toLowerCase());var C=Sw();a.copyToHitData(J.m.Pc,C.width+"x"+C.height);E(145)&&a.copyToHitData(J.m.nf,void 0,yN);E(87)&&jv()&&a.copyToHitData(J.m.md,"1")}},XN=function(a,b){var c=P(a,O.A.Kf);c=c||0;var d=hp(J.m.U),e=c===0||!b&&d||!!P(a,O.A.Gf)||!!Jv(a,J.m.ii);R(a,O.A.Ji,e);e&&R(a,O.A.Kf,60);return d},YN=function(a){R(a,O.A.xg,!1);R(a,O.A.Od,!1);if(!dy(a)&&!P(a,O.A.xd)&&N(a.D,J.m.Pb)!==!1&&XJ()&&hp(J.m.U)&&(!E(143)||hp(J.m.ia))){var b=ey(a);(P(a,O.A.ne)||
N(a.D,J.m.ii))&&R(a,O.A.xg,!!b);b&&P(a,O.A.Ji)&&P(a,O.A.Hi)&&R(a,O.A.Od,!0)}},ZN=function(a){R(a,O.A.yg,!1);R(a,O.A.zg,!1);if(!(E(187)&&jo()||!jk()||dy(a)||P(a,O.A.xd))&&P(a,O.A.Ji)){var b=P(a,O.A.Od);P(a,O.A.Jf)&&(b?R(a,O.A.zg,!0):E(176)&&R(a,O.A.yg,!0))}},bO=function(a){a.copyToHitData(J.m.ri);for(var b=N(a.D,J.m.ji)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(J.m.ri,d.traffic_type);ML(3);break}}},kO=function(a){a.copyToHitData(J.m.Lk);N(a.D,J.m.Wg)&&(S(a,J.m.Wg,!0),dy(a)||
xN(a))},gO=function(a){a.copyToHitData(J.m.Qa);a.copyToHitData(J.m.Xb)},WN=function(a){cw(a,"google_ng")&&!ho()?a.copyToHitData(J.m.be,1):Wv(a)},iO=function(a){var b=N(a.D,J.m.Nc);b&&ML(12);P(a,O.A.Ke)&&ML(14);var c=zm(om());(b||Im(c)||c&&c.parent&&c.context&&c.context.source===5)&&ML(19)},HN=function(a){if(uN(a.target.destinationId))M(28),a.isAborted=!0;else if(E(144)){var b=ym();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(uN(b.destinations[c])){M(125);a.isAborted=
!0;break}}},dO=function(a){Hl("attribution-reporting")&&S(a,J.m.fd,"1")},IN=function(a){if(BN.lp.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=by(a);b&&b.blacklisted&&(a.isAborted=!0)}},$N=function(a){var b=function(c){return!!c&&c.conversion};R(a,O.A.Hf,b(by(a)));P(a,O.A.If)&&R(a,O.A.vl,b(by(a,"first_visit")));P(a,O.A.ne)&&R(a,O.A.xl,b(by(a,"session_start")))},aO=function(a){ro.hasOwnProperty(a.eventName)&&(R(a,O.A.tl,!0),a.copyToHitData(J.m.sa),a.copyToHitData(J.m.Ua))},
hO=function(a){if(!dy(a)&&P(a,O.A.Hf)&&hp(J.m.U)&&cw(a,"ga4_ads_linked",!1)){var b=Hv(a),c=bu(b.prefix),d=Cv(c);S(a,J.m.Ud,d.yh);S(a,J.m.Wd,d.Ah);S(a,J.m.Vd,d.zh)}},eO=function(a){if(E(122)){var b=jo();b&&R(a,O.A.eo,b)}},TN=function(a){R(a,O.A.Hi,ey(a)&&N(a.D,J.m.Pb)!==!1&&XJ()&&!ho())};function GN(a){sb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Xb]||{};sb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var mO=function(a){if(!lO(a)){var b=!1,c=function(){!b&&lO(a)&&(b=!0,Ic(z,"visibilitychange",c),E(5)&&Ic(z,"prerenderingchange",c),M(55))};Hc(z,"visibilitychange",c);E(5)&&Hc(z,"prerenderingchange",c);M(54)}},lO=function(a){if(E(5)&&"prerendering"in z?z.prerendering:z.visibilityState==="prerender")return!1;a();return!0};function nO(a,b){mO(function(){var c=Cp(a);if(c){var d=oO(c,b);Bq(a,d,Lm.X.Hc)}});}function oO(a,b){var c=function(){};var d=new FN(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[O.A.xd]=!0);d.kq(g,h,m)};pO(a,d,b);return c}
function pO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[O.A.Uj]=!0,e),deferrable:!0};d.nq(function(){IL=!0;Cq.flush();d.Bh()>=1E3&&qc.sendBeacon!==void 0&&Dq(J.m.Td,{},a.id,f);b.flush();d.Fm(function(){IL=!1;d.Fm()})});};var qO=oO;function sO(a,b,c){var d=this;}sO.M="internal.gtagConfig";
function uO(a,b){}
uO.publicName="gtagSet";function vO(){var a={};return a};function wO(a){}wO.M="internal.initializeServiceWorker";function xO(a,b){}xO.publicName="injectHiddenIframe";var yO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function zO(a,b,c,d,e){}zO.M="internal.injectHtml";var DO={};
function FO(a,b,c,d){}var GO={dl:1,id:1},HO={};
function IO(a,b,c,d){}E(160)?IO.publicName="injectScript":FO.publicName="injectScript";IO.M="internal.injectScript";function JO(){return ko()}JO.M="internal.isAutoPiiEligible";function KO(a){var b=!0;return b}KO.publicName="isConsentGranted";function LO(a){var b=!1;return b}LO.M="internal.isDebugMode";function MO(){return io()}MO.M="internal.isDmaRegion";function NO(a){var b=!1;return b}NO.M="internal.isEntityInfrastructure";function OO(a){var b=!1;if(!oh(a))throw G(this.getName(),["number"],[a]);b=E(a);return b}OO.M="internal.isFeatureEnabled";function PO(){var a=!1;return a}PO.M="internal.isFpfe";function QO(){var a=!1;return a}QO.M="internal.isGcpConversion";function RO(){var a=!1;return a}RO.M="internal.isLandingPage";function SO(){var a;return a}SO.M="internal.isSafariPcmEligibleBrowser";function TO(){var a=Lh(function(b){iF(this).log("error",b)});a.publicName="JSON";return a};function UO(a){var b=void 0;return zd(b)}UO.M="internal.legacyParseUrl";function VO(){return!1}
var WO={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function XO(){}XO.publicName="logToConsole";function YO(a,b){}YO.M="internal.mergeRemoteConfig";function ZO(a,b,c){c=c===void 0?!0:c;var d=[];return zd(d)}ZO.M="internal.parseCookieValuesFromString";function $O(a){var b=void 0;if(typeof a!=="string")return;a&&Eb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=zd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Pk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),v=t[0],u=Ik(t.splice(1).join("="))||"";u=u.replace(/\+/g," ");p.hasOwnProperty(v)?typeof p[v]==="string"?p[v]=[p[v],u]:p[v].push(u):p[v]=u}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=zd(n);
return b}$O.publicName="parseUrl";function aP(a){}aP.M="internal.processAsNewEvent";function bP(a,b,c){var d;return d}bP.M="internal.pushToDataLayer";function cP(a){var b=xa.apply(1,arguments),c=!1;if(!jh(a))throw G(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(yd(f.value,this.K,1));try{I.apply(null,d),c=!0}catch(g){return!1}return c}cP.publicName="queryPermission";function dP(a){var b=this;}dP.M="internal.queueAdsTransmission";function eP(a,b){var c=void 0;return c}eP.publicName="readAnalyticsStorage";function fP(){var a="";return a}fP.publicName="readCharacterSet";function gP(){return Rj}gP.M="internal.readDataLayerName";function hP(){var a="";return a}hP.publicName="readTitle";function iP(a,b){var c=this;if(!jh(a)||!fh(b))throw G(this.getName(),["string","function"],arguments);Cw(a,function(d){b.invoke(c.K,zd(d,c.K,1))});}iP.M="internal.registerCcdCallback";function jP(a,b){return!0}jP.M="internal.registerDestination";var kP=["config","event","get","set"];function lP(a,b,c){}lP.M="internal.registerGtagCommandListener";function mP(a,b){var c=!1;return c}mP.M="internal.removeDataLayerEventListener";function nP(a,b){}
nP.M="internal.removeFormData";function oP(){}oP.publicName="resetDataLayer";function pP(a,b,c){var d=void 0;return d}pP.M="internal.scrubUrlParams";function qP(a){}qP.M="internal.sendAdsHit";function rP(a,b,c,d){if(arguments.length<2||!dh(d)||!dh(c))throw G(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?yd(c):{},f=yd(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?yd(d):{},m=iF(this);h.originatingEntity=YF(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};jd(e,q);var r={};jd(h,r);var t=Hw(p,b,q);Kw(t,h.eventId||m.eventId,r)}}}rP.M="internal.sendGtagEvent";function sP(a,b,c){}sP.publicName="sendPixel";function tP(a,b){}tP.M="internal.setAnchorHref";function uP(a){}uP.M="internal.setContainerConsentDefaults";function vP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}vP.publicName="setCookie";function wP(a){}wP.M="internal.setCorePlatformServices";function xP(a,b){}xP.M="internal.setDataLayerValue";function yP(a){}yP.publicName="setDefaultConsentState";function zP(a,b){}zP.M="internal.setDelegatedConsentType";function AP(a,b){}AP.M="internal.setFormAction";function BP(a,b,c){c=c===void 0?!1:c;}BP.M="internal.setInCrossContainerData";function CP(a,b,c){return!1}CP.publicName="setInWindow";function DP(a,b,c){}DP.M="internal.setProductSettingsParameter";function EP(a,b,c){if(!jh(a)||!jh(b)||arguments.length!==3)throw G(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Fq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!id(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=yd(c,this.K,1);}EP.M="internal.setRemoteConfigParameter";function FP(a,b){}FP.M="internal.setTransmissionMode";function GP(a,b,c,d){var e=this;}GP.publicName="sha256";function HP(a,b,c){}
HP.M="internal.sortRemoteConfigParameters";function IP(a){}IP.M="internal.storeAdsBraidLabels";function JP(a,b){var c=void 0;return c}JP.M="internal.subscribeToCrossContainerData";var KP={},LP={};KP.getItem=function(a){var b=null;I(this,"access_template_storage");var c=iF(this).Kb();LP[c]&&(b=LP[c].hasOwnProperty("gtm."+a)?LP[c]["gtm."+a]:null);return b};KP.setItem=function(a,b){I(this,"access_template_storage");var c=iF(this).Kb();LP[c]=LP[c]||{};LP[c]["gtm."+a]=b;};
KP.removeItem=function(a){I(this,"access_template_storage");var b=iF(this).Kb();if(!LP[b]||!LP[b].hasOwnProperty("gtm."+a))return;delete LP[b]["gtm."+a];};KP.clear=function(){I(this,"access_template_storage"),delete LP[iF(this).Kb()];};KP.publicName="templateStorage";function MP(a,b){var c=!1;return c}MP.M="internal.testRegex";function NP(a){var b;return b};function OP(a,b){var c;return c}OP.M="internal.unsubscribeFromCrossContainerData";function PP(a){}PP.publicName="updateConsentState";function QP(a){var b=!1;return b}QP.M="internal.userDataNeedsEncryption";var RP;function SP(a,b,c){RP=RP||new Wh;RP.add(a,b,c)}function TP(a,b){var c=RP=RP||new Wh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=jb(b)?rh(a,b):sh(a,b)}
function UP(){return function(a){var b;var c=RP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.sb();if(e){var f=!1,g=e.Kb();if(g){yh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function VP(){var a=function(c){return void TP(c.M,c)},b=function(c){return void SP(c.publicName,c)};b(cF);b(jF);b(xG);b(zG);b(AG);b(HG);b(JG);b(EH);b(TO());b(GH);b(aL);b(bL);b(xL);b(yL);b(zL);b(FL);b(uO);b(xO);b(KO);b(XO);b($O);b(cP);b(fP);b(hP);b(sP);b(vP);b(yP);b(CP);b(GP);b(KP);b(PP);SP("Math",wh());SP("Object",Uh);SP("TestHelper",Yh());SP("assertApi",th);SP("assertThat",uh);SP("decodeUri",zh);SP("decodeUriComponent",Ah);SP("encodeUri",Bh);SP("encodeUriComponent",Ch);SP("fail",Hh);SP("generateRandom",
Ih);SP("getTimestamp",Jh);SP("getTimestampMillis",Jh);SP("getType",Kh);SP("makeInteger",Mh);SP("makeNumber",Nh);SP("makeString",Oh);SP("makeTableMap",Ph);SP("mock",Sh);SP("mockObject",Th);SP("fromBase64",UK,!("atob"in x));SP("localStorage",WO,!VO());SP("toBase64",NP,!("btoa"in x));a(bF);a(fF);a(zF);a(LF);a(SF);a(XF);a(mG);a(vG);a(yG);a(BG);a(CG);a(DG);a(EG);a(FG);a(GG);a(IG);a(KG);a(DH);a(FH);a(HH);a(IH);a(JH);a(KH);a(LH);a(MH);a(RH);a(ZH);a($H);a(kI);a(pI);a(uI);a(DI);a(II);a(VI);a(XI);a(kJ);a(lJ);
a(nJ);a(SK);a(TK);a(VK);a(WK);a(XK);a(YK);a(ZK);a(dL);a(eL);a(fL);a(gL);a(hL);a(iL);a(jL);a(kL);a(lL);a(mL);a(nL);a(oL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(wL);a(AL);a(BL);a(CL);a(DL);a(EL);a(HL);a(sO);a(wO);a(zO);a(IO);a(JO);a(LO);a(MO);a(NO);a(OO);a(PO);a(QO);a(RO);a(SO);a(UO);a(kG);a(YO);a(ZO);a(aP);a(bP);a(dP);a(gP);a(iP);a(jP);a(lP);a(mP);a(nP);a(pP);a(qP);a(rP);a(tP);a(uP);a(wP);a(xP);a(zP);a(AP);a(BP);a(DP);a(EP);a(FP);a(HP);a(IP);a(JP);a(MP);a(OP);a(QP);TP("internal.IframingStateSchema",
vO());
E(104)&&a(cL);E(160)?b(IO):b(FO);E(177)&&b(eP);return UP()};var $E;
function WP(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;$E=new Te;XP();Af=ZE();var e=$E,f=VP(),g=new rd("require",f);g.Ta();e.C.C.set("require",g);Pa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Wf(n,d[m]);try{$E.execute(n),E(120)&&dl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Of=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");fk[q]=["sandboxedScripts"]}YP(b)}function XP(){$E.Xc(function(a,b,c){rp.SANDBOXED_JS_SEMAPHORE=rp.SANDBOXED_JS_SEMAPHORE||0;rp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{rp.SANDBOXED_JS_SEMAPHORE--}})}function YP(a){a&&sb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");fk[e]=fk[e]||[];fk[e].push(b)}})};function ZP(a){Kw(Ew("developer_id."+a,!0),0,{})};var $P=Array.isArray;function aQ(a,b){return jd(a,b||null)}function W(a){return window.encodeURIComponent(a)}function bQ(a,b,c){Gc(a,b,c)}
function cQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Jk(Pk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function dQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function eQ(a){var b={},c=a.eventSettingsVariable;if(c)for(var d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);if(a.eventSettingsTable){var e=dQ(a.eventSettingsTable,"parameter","parameterValue");e&&(b=aQ(e,b))}return b}var fQ=x.clearTimeout,gQ=x.setTimeout;function hQ(a,b,c){if(Er()){b&&Jc(b)}else return Cc(a,b,c,void 0)}function iQ(){return x.location.href}function jQ(a,b){return pk(a,b||2)}function kQ(a,b){x[a]=b}function lQ(a,b,c){b&&(x[a]===void 0||c&&!x[a])&&(x[a]=b);return x[a]}function mQ(a,b){if(Er()){b&&Jc(b)}else Ec(a,b)}
var nQ={};var X={securityGroups:{}};
X.securityGroups.access_template_storage=["google"],X.__access_template_storage=function(){return{assert:function(){},T:function(){return{}}}},X.__access_template_storage.F="access_template_storage",X.__access_template_storage.isVendorTemplate=!0,X.__access_template_storage.priorityOverride=0,X.__access_template_storage.isInfrastructure=!1,X.__access_template_storage["5"]=!1;

X.securityGroups.v=["google"],X.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=jQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},X.__v.F="v",X.__v.isVendorTemplate=!0,X.__v.priorityOverride=0,X.__v.isInfrastructure=!0,X.__v["5"]=!0;
X.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){X.__read_event_data=b;X.__read_event_data.F="read_event_data";X.__read_event_data.isVendorTemplate=!0;X.__read_event_data.priorityOverride=0;X.__read_event_data.isInfrastructure=!1;X.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!lb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Hg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

X.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){X.__detect_youtube_activity_events=b;X.__detect_youtube_activity_events.F="detect_youtube_activity_events";X.__detect_youtube_activity_events.isVendorTemplate=!0;X.__detect_youtube_activity_events.priorityOverride=0;X.__detect_youtube_activity_events.isInfrastructure=!1;X.__detect_youtube_activity_events["5"]=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},T:a}})}();



X.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){X.__detect_history_change_events=b;X.__detect_history_change_events.F="detect_history_change_events";X.__detect_history_change_events.isVendorTemplate=!0;X.__detect_history_change_events.priorityOverride=0;X.__detect_history_change_events.isInfrastructure=!1;X.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();


X.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){X.__detect_link_click_events=b;X.__detect_link_click_events.F="detect_link_click_events";X.__detect_link_click_events.isVendorTemplate=!0;X.__detect_link_click_events.priorityOverride=0;X.__detect_link_click_events.isInfrastructure=!1;X.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},T:a}})}();
X.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){X.__detect_form_submit_events=b;X.__detect_form_submit_events.F="detect_form_submit_events";X.__detect_form_submit_events.isVendorTemplate=!0;X.__detect_form_submit_events.priorityOverride=0;X.__detect_form_submit_events.isInfrastructure=!1;X.__detect_form_submit_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&
f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},T:a}})}();
X.securityGroups.read_container_data=["google"],X.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},X.__read_container_data.F="read_container_data",X.__read_container_data.isVendorTemplate=!0,X.__read_container_data.priorityOverride=0,X.__read_container_data.isInfrastructure=!1,X.__read_container_data["5"]=!1;
X.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){X.__listen_data_layer=b;X.__listen_data_layer.F="listen_data_layer";X.__listen_data_layer.isVendorTemplate=!0;X.__listen_data_layer.priorityOverride=0;X.__listen_data_layer.isInfrastructure=!1;X.__listen_data_layer["5"]=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!lb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},T:a}})}();
X.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){X.__detect_user_provided_data=b;X.__detect_user_provided_data.F="detect_user_provided_data";X.__detect_user_provided_data.isVendorTemplate=!0;X.__detect_user_provided_data.priorityOverride=0;X.__detect_user_provided_data.isInfrastructure=!1;X.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();



X.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){X.__get_url=b;X.__get_url.F="get_url";X.__get_url.isVendorTemplate=!0;X.__get_url.priorityOverride=0;X.__get_url.isInfrastructure=!1;X.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!lb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!lb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();




X.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){X.__gct=b;X.__gct.F="gct";X.__gct.isVendorTemplate=!0;X.__gct.priorityOverride=0;X.__gct.isInfrastructure=!1;X.__gct["5"]=!1})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[J.m.qf]=d);c[J.m.Og]=b.vtp_eventSettings;c[J.m.vk]=b.vtp_dynamicEventSettings;c[J.m.ce]=b.vtp_googleSignals===1;c[J.m.Mk]=b.vtp_foreignTld;c[J.m.Kk]=b.vtp_restrictDomain===
1;c[J.m.ji]=b.vtp_internalTrafficResults;var e=J.m.Pa,f=b.vtp_linker;f&&f[J.m.ma]&&(f[J.m.ma]=a(f[J.m.ma]));c[e]=f;var g=J.m.ki,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Hq(b.vtp_trackingId,c);nO(b.vtp_trackingId,b.vtp_gtmEventId);Jc(b.vtp_gtmOnSuccess)})}();



X.securityGroups.get=["google"],X.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Hw(String(b.streamId),d,c);Kw(f,e.eventId,e);a.vtp_gtmOnSuccess()},X.__get.F="get",X.__get.isVendorTemplate=!0,X.__get.priorityOverride=0,X.__get.isInfrastructure=!1,X.__get["5"]=!1;
X.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){X.__detect_scroll_events=b;X.__detect_scroll_events.F="detect_scroll_events";X.__detect_scroll_events.isVendorTemplate=!0;X.__detect_scroll_events.priorityOverride=0;X.__detect_scroll_events.isInfrastructure=!1;X.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();



X.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){X.__detect_form_interaction_events=b;X.__detect_form_interaction_events.F="detect_form_interaction_events";X.__detect_form_interaction_events.isVendorTemplate=!0;X.__detect_form_interaction_events.priorityOverride=0;X.__detect_form_interaction_events.isInfrastructure=!1;X.__detect_form_interaction_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();
var up={dataLayer:qk,callback:function(a){ek.hasOwnProperty(a)&&jb(ek[a])&&ek[a]();delete ek[a]},bootstrap:0};
function oQ(){tp();Cm();HB();Cb(fk,X.securityGroups);var a=zm(om()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;So(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||M(142);Nf={So:bg}}var pQ=!1;
function bo(){try{if(pQ||!Jm()){Nj();Kj.P=Ni(18,"");
Kj.Fb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Kj.Xa="ad_storage|analytics_storage|ad_user_data";Kj.Ba="56n0";Kj.Ba="56n0";if(E(109)){}Ha[8]=!0;var a=sp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});Zo(a);qp();QE();fr();wp();if(Dm()){hG();xC().removeExternalRestrictions(wm());}else{Qy();Lf();Gf=X;Hf=AE;dg=new kg;WP();oQ();$n||(Zn=eo());
np();MD();ZC();sD=!1;z.readyState==="complete"?uD():Hc(x,"load",uD);TC();dl&&(jq(xq),x.setInterval(wq,864E5),jq(RE),jq(kC),jq(Yz),jq(Aq),jq(WE),jq(vC),E(120)&&(jq(pC),jq(qC),jq(rC)),SE={},jq(TE),Qi());el&&(Nn(),Qp(),OD(),SD(),QD(),Dn("bt",String(Kj.C?2:Kj.N?1:0)),Dn("ct",String(Kj.C?0:Kj.N?1:Er()?2:3)),PD());qE();Xn(1);iG();WD();dk=zb();up.bootstrap=dk;Kj.ka&&LD();E(109)&&rA();E(134)&&(typeof x.name==="string"&&Eb(x.name,"web-pixel-sandbox-CUSTOM")&&Zc()?ZP("dMDg0Yz"):x.Shopify&&(ZP("dN2ZkMj"),Zc()&&ZP("dNTU0Yz")))}}}catch(b){Xn(4),tq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Fo(n)&&(m=h.nl)}function c(){m&&tc?g(m):a()}if(!x[Ni(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Pk(z.referrer);d=Lk(e,"host")===Ni(38,"cct.google")}if(!d){var f=fs(Ni(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Ni(37,"__TAGGY_INSTALLED")]=!0,Cc(Ni(40,"https://cct.google/taggy/agent.js")))}var g=function(v){var u="GTM",w="GTM";Xj&&(u="OGT",w="GTAG");
var y=Ni(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Cc("https://"+Oj.Ag+"/debug/bootstrap?id="+hg.ctid+"&src="+w+"&cond="+String(v)+"&gtm="+Ir()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:tc,containerProduct:u,debug:!1,id:hg.ctid,targetRef:{ctid:hg.ctid,isDestination:um()},aliases:xm(),destinations:vm()}};C.data.resume=function(){a()};Oj.Xm&&(C.data.initialPublish=!0);A.push(C)},h={jo:1,ql:2,Fl:3,ik:4,nl:5};h[h.jo]="GTM_DEBUG_LEGACY_PARAM";h[h.ql]="GTM_DEBUG_PARAM";h[h.Fl]="REFERRER";
h[h.ik]="COOKIE";h[h.nl]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Jk(x.location,"query",!1,void 0,"gtm_debug");Fo(p)&&(m=h.ql);if(!m&&z.referrer){var q=Pk(z.referrer);Lk(q,"host")===Ni(24,"tagassistant.google.com")&&(m=h.Fl)}if(!m){var r=fs("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.ik)}m||b();if(!m&&Eo(n)){var t=!1;Hc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&pQ&&!eo()["0"]?ao():bo()});

})()

