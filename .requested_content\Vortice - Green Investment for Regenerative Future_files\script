"use strict";(()=>{var ne,_,re;function Ee(){ne=Intl.DateTimeFormat().resolvedOptions(),_=ne.timeZone,re=ne.locale}var ie=null;function T(){if(!ie){let e=document.currentScript;ie={src:e.src,framerSiteId:e?e.getAttribute("data-fid"):null,trackNavigation:!(e!=null&&e.hasAttribute("data-no-nt")),cdn:"https://framerusercontent.com/sites/"}}return ie}function E(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}function st(){return`${E()}${E()}-${E()}-${E()}-${E()}-${E()}${E()}${E()}`}var f=class{constructor(t,r){this.event=t;this.data=r}serialize(){return{source:"framer.site",timestamp:Date.now(),data:{type:"track",uuid:st(),event:this.event,...this.data}}}};var be=new Set,ae=e=>be.forEach(({callback:t,on:r})=>r===e&&t()),C=(e,t="lazy")=>be.add({callback:e,on:t});addEventListener("visibilitychange",()=>{document.hidden&&ae("lazy")});addEventListener("pagehide",()=>ae("lazy"));addEventListener("load",()=>ae("load"));var ct="fetchLater"in window,O,q,M;function dt(e,t,r,a){let i=JSON.stringify(t);try{return window.fetchLater(e,{method:"POST",body:i,signal:a,activateAfter:r?3e3:10*6e4})}catch{return Ce(e,i),{activated:!0}}}function ut(e,t,r){return ct?(q&&q.abort(),O!=null&&O.activated&&M&&(M.length=0),M??=[],M=M.concat(t),q=new AbortController,O=dt(e,M,r,q.signal),O):!1}function Ce(e,t){fetch(e,{method:"POST",body:t})}function lt(e,t){return navigator.sendBeacon(e,t)}function we(e,t,r=!1){if(!ut(e,t,r)){let a=JSON.stringify(t);lt(e,a)||Ce(e,a)}}var J=new Set;function Le(){for(let e of J)e();J.clear()}var D=window.scheduler,mt=D&&"yield"in D,ft=D&&"postTask"in D;function w(e=!1){return new Promise(t=>{if(J.add(t),!document.hidden){requestAnimationFrame(async()=>{let r=()=>{J.delete(t),t()};e?mt?(await D.yield(),r()):ft?D.postTask(r):r():setTimeout(r,1)});return}Le()})}C(Le,"lazy");var ke=T(),gt=new URL(ke.src),pt=`${gt.origin}/anonymous`;function Pe(e,t=!1){if(!location.protocol.startsWith("http"))return;let r={framerSiteId:ke.framerSiteId,origin:document.location.origin,pathname:document.location.pathname,search:document.location.search,visitTimeOrigin:performance.timeOrigin};we(pt,e.map(a=>({...a,data:{...a.data,context:{...r,...a.data.context}}})),t)}function Me(e,t){return e==="eager"||t==="eager"?"eager":t??e}var j=new Set,oe=!1;function se(){if(j.size===0)return;if(!oe){oe=!0,queueMicrotask(se);return}let e=[];j.forEach(t=>t.forEach(r=>e.push(r.serialize()))),j.clear(),Pe(e),oe=!1}async function y(e,t="lazy"){if(e.length!==0){if(t==="eager"){await w(),Pe(e.map(r=>r.serialize()),!0);return}j.add(e),document.hidden&&se()}}C(se,"lazy");var x="__framer_events";function De(){window[x]||(window[x]=[]);function e(t){let r,a=t.map(i=>{let[o,n,s]=i;return r=Me(r,s),new f(o,n)});y(a,r??"eager")}window[x].length>0&&(e(window[x]),window[x].length=0),window[x].push=(...t)=>(e(t),-1)}var vt=T();function W(e){let t=[new f("published_site_pageview",{referrer:(e==null?void 0:e.initialReferrer)||null,url:location.href,hostname:location.hostname||null,pathname:location.pathname||null,hash:location.hash||null,search:location.search||null,framerSiteId:vt.framerSiteId,timezone:_,locale:re})];y(t,"eager")}function xe(){addEventListener("popstate",()=>W());let e=history.pushState;history.pushState=(...t)=>{e.apply(history,t),W()}}var ue,Fe,I=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},G=function(e){if(document.readyState==="loading")return"loading";var t=I();if(t){if(e<t.domInteractive)return"loading";if(t.domContentLoadedEventStart===0||e<t.domContentLoadedEventStart)return"dom-interactive";if(t.domComplete===0||e<t.domComplete)return"dom-content-loaded"}return"complete"},ht=function(e){var t=e.nodeName;return e.nodeType===1?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},fe=function(e,t){var r="";try{for(;e&&e.nodeType!==9;){var a=e,i=a.id?"#"+a.id:ht(a)+(a.classList&&a.classList.value&&a.classList.value.trim()&&a.classList.value.trim().length?"."+a.classList.value.trim().replace(/\s+/g,"."):"");if(r.length+i.length>(t||100)-1)return r||i;if(r=r?i+">"+r:i,a.id)break;e=a.parentNode}}catch{}return r},Ne=-1,We=function(){return Ne},A=function(e){addEventListener("pageshow",function(t){t.persisted&&(Ne=t.timeStamp,e(t))},!0)},Z=function(){var e=I();return e&&e.activationStart||0},v=function(e,t){var r=I(),a="navigate";return We()>=0?a="back-forward-cache":r&&(document.prerendering||Z()>0?a="prerender":document.wasDiscarded?a="restore":r.type&&(a=r.type.replace(/_/g,"-"))),{name:e,value:t===void 0?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:a}},B=function(e,t,r){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var a=new PerformanceObserver(function(i){Promise.resolve().then(function(){t(i.getEntries())})});return a.observe(Object.assign({type:e,buffered:!0},r||{})),a}}catch{}},h=function(e,t,r,a){var i,o;return function(n){t.value>=0&&(n||a)&&((o=t.value-(i||0))||i===void 0)&&(i=t.value,t.delta=o,t.rating=function(s,d){return s>d[1]?"poor":s>d[0]?"needs-improvement":"good"}(t.value,r),e(t))}},ge=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},X=function(e){document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&e()})},pe=function(e){var t=!1;return function(){t||(e(),t=!0)}},F=-1,Re=function(){return document.visibilityState!=="hidden"||document.prerendering?1/0:0},Q=function(e){document.visibilityState==="hidden"&&F>-1&&(F=e.type==="visibilitychange"?e.timeStamp:0,Tt())},ze=function(){addEventListener("visibilitychange",Q,!0),addEventListener("prerenderingchange",Q,!0)},Tt=function(){removeEventListener("visibilitychange",Q,!0),removeEventListener("prerenderingchange",Q,!0)},$e=function(){return F<0&&(F=Re(),ze(),A(function(){setTimeout(function(){F=Re(),ze()},0)})),{get firstHiddenTime(){return F}}},Y=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},Ie=[1800,3e3],He=function(e,t){t=t||{},Y(function(){var r,a=$e(),i=v("FCP"),o=B("paint",function(n){n.forEach(function(s){s.name==="first-contentful-paint"&&(o.disconnect(),s.startTime<a.firstHiddenTime&&(i.value=Math.max(s.startTime-Z(),0),i.entries.push(s),r(!0)))})});o&&(r=h(e,i,Ie,t.reportAllChanges),A(function(n){i=v("FCP"),r=h(e,i,Ie,t.reportAllChanges),ge(function(){i.value=performance.now()-n.timeStamp,r(!0)})}))})},Ae=[.1,.25],Ue=function(e,t){(function(r,a){a=a||{},He(pe(function(){var i,o=v("CLS",0),n=0,s=[],d=function(l){l.forEach(function(c){if(!c.hadRecentInput){var m=s[0],g=s[s.length-1];n&&c.startTime-g.startTime<1e3&&c.startTime-m.startTime<5e3?(n+=c.value,s.push(c)):(n=c.value,s=[c])}}),n>o.value&&(o.value=n,o.entries=s,i())},u=B("layout-shift",d);u&&(i=h(r,o,Ae,a.reportAllChanges),X(function(){d(u.takeRecords()),i(!0)}),A(function(){n=0,o=v("CLS",0),i=h(r,o,Ae,a.reportAllChanges),ge(function(){return i()})}),setTimeout(i,0))}))})(function(r){var a=function(i){var o,n={};if(i.entries.length){var s=i.entries.reduce(function(u,l){return u&&u.value>l.value?u:l});if(s&&s.sources&&s.sources.length){var d=(o=s.sources).find(function(u){return u.node&&u.node.nodeType===1})||o[0];d&&(n={largestShiftTarget:fe(d.node),largestShiftTime:s.startTime,largestShiftValue:s.value,largestShiftSource:d,largestShiftEntry:s,loadState:G(s.startTime)})}}return Object.assign(i,{attribution:n})}(r);e(a)},t)},qe=function(e,t){He(function(r){var a=function(i){var o={timeToFirstByte:0,firstByteToFCP:i.value,loadState:G(We())};if(i.entries.length){var n=I(),s=i.entries[i.entries.length-1];if(n){var d=n.activationStart||0,u=Math.max(0,n.responseStart-d);o={timeToFirstByte:u,firstByteToFCP:i.value-u,loadState:G(i.entries[0].startTime),navigationEntry:n,fcpEntry:s}}}return Object.assign(i,{attribution:o})}(r);e(a)},t)},Je=0,ce=1/0,V=0,yt=function(e){e.forEach(function(t){t.interactionId&&(ce=Math.min(ce,t.interactionId),V=Math.max(V,t.interactionId),Je=V?(V-ce)/7+1:0)})},je=function(){return ue?Je:performance.interactionCount||0},St=function(){"interactionCount"in performance||ue||(ue=B("event",yt,{type:"event",buffered:!0,durationThreshold:0}))},p=[],$=new Map,Ve=0,Et=function(){var e=Math.min(p.length-1,Math.floor((je()-Ve)/50));return p[e]},Ge=[],bt=function(e){if(Ge.forEach(function(i){return i(e)}),e.interactionId||e.entryType==="first-input"){var t=p[p.length-1],r=$.get(e.interactionId);if(r||p.length<10||e.duration>t.latency){if(r)e.duration>r.latency?(r.entries=[e],r.latency=e.duration):e.duration===r.latency&&e.startTime===r.entries[0].startTime&&r.entries.push(e);else{var a={id:e.interactionId,latency:e.duration,entries:[e]};$.set(a.id,a),p.push(a)}p.sort(function(i,o){return o.latency-i.latency}),p.length>10&&p.splice(10).forEach(function(i){return $.delete(i.id)})}}},ve=function(e){var t=self.requestIdleCallback||self.setTimeout,r=-1;return e=pe(e),document.visibilityState==="hidden"?e():(r=t(e),X(e)),r},Be=[200,500],Ct=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},Y(function(){var r;St();var a,i=v("INP"),o=function(s){ve(function(){s.forEach(bt);var d=Et();d&&d.latency!==i.value&&(i.value=d.latency,i.entries=d.entries,a())})},n=B("event",o,{durationThreshold:(r=t.durationThreshold)!==null&&r!==void 0?r:40});a=h(e,i,Be,t.reportAllChanges),n&&(n.observe({type:"first-input",buffered:!0}),X(function(){o(n.takeRecords()),a(!0)}),A(function(){Ve=je(),p.length=0,$.clear(),i=v("INP"),a=h(e,i,Be,t.reportAllChanges)}))}))},R=[],b=[],le=0,he=new WeakMap,z=new Map,me=-1,wt=function(e){R=R.concat(e),Qe()},Qe=function(){me<0&&(me=ve(Lt))},Lt=function(){z.size>10&&z.forEach(function(n,s){$.has(s)||z.delete(s)});var e=p.map(function(n){return he.get(n.entries[0])}),t=b.length-50;b=b.filter(function(n,s){return s>=t||e.includes(n)});for(var r=new Set,a=0;a<b.length;a++){var i=b[a];Ze(i.startTime,i.processingEnd).forEach(function(n){r.add(n)})}var o=R.length-1-50;R=R.filter(function(n,s){return n.startTime>le&&s>o||r.has(n)}),me=-1};Ge.push(function(e){e.interactionId&&e.target&&!z.has(e.interactionId)&&z.set(e.interactionId,e.target)},function(e){var t,r=e.startTime+e.duration;le=Math.max(le,e.processingEnd);for(var a=b.length-1;a>=0;a--){var i=b[a];if(Math.abs(r-i.renderTime)<=8){(t=i).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:r,entries:[e]},b.push(t)),(e.interactionId||e.entryType==="first-input")&&he.set(e,t),Qe()});var Ze=function(e,t){for(var r,a=[],i=0;r=R[i];i++)if(!(r.startTime+r.duration<e)){if(r.startTime>t)break;a.push(r)}return a},Xe=function(e,t){Fe||(Fe=B("long-animation-frame",wt)),Ct(function(r){var a=function(i){var o=i.entries[0],n=he.get(o),s=o.processingStart,d=n.processingEnd,u=n.entries.sort(function(S,H){return S.processingStart-H.processingStart}),l=Ze(o.startTime,d),c=i.entries.find(function(S){return S.target}),m=c&&c.target||z.get(o.interactionId),g=[o.startTime+o.duration,d].concat(l.map(function(S){return S.startTime+S.duration})),k=Math.max.apply(Math,g),K={interactionTarget:fe(m),interactionTargetElement:m,interactionType:o.name.startsWith("key")?"keyboard":"pointer",interactionTime:o.startTime,nextPaintTime:k,processedEventEntries:u,longAnimationFrameEntries:l,inputDelay:s-o.startTime,processingDuration:d-s,presentationDelay:Math.max(k-d,0),loadState:G(o.startTime)};return Object.assign(i,{attribution:K})}(r);e(a)},t)},_e=[2500,4e3],de={},Ye=function(e,t){(function(r,a){a=a||{},Y(function(){var i,o=$e(),n=v("LCP"),s=function(l){a.reportAllChanges||(l=l.slice(-1)),l.forEach(function(c){c.startTime<o.firstHiddenTime&&(n.value=Math.max(c.startTime-Z(),0),n.entries=[c],i())})},d=B("largest-contentful-paint",s);if(d){i=h(r,n,_e,a.reportAllChanges);var u=pe(function(){de[n.id]||(s(d.takeRecords()),d.disconnect(),de[n.id]=!0,i(!0))});["keydown","click"].forEach(function(l){addEventListener(l,function(){return ve(u)},{once:!0,capture:!0})}),X(u),A(function(l){n=v("LCP"),i=h(r,n,_e,a.reportAllChanges),ge(function(){n.value=performance.now()-l.timeStamp,de[n.id]=!0,i(!0)})})}})})(function(r){var a=function(i){var o={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:i.value};if(i.entries.length){var n=I();if(n){var s=n.activationStart||0,d=i.entries[i.entries.length-1],u=d.url&&performance.getEntriesByType("resource").filter(function(k){return k.name===d.url})[0],l=Math.max(0,n.responseStart-s),c=Math.max(l,u?(u.requestStart||u.startTime)-s:0),m=Math.max(c,u?u.responseEnd-s:0),g=Math.max(m,d.startTime-s);o={element:fe(d.element),timeToFirstByte:l,resourceLoadDelay:c-l,resourceLoadDuration:m-c,elementRenderDelay:g-m,navigationEntry:n,lcpEntry:d},d.url&&(o.url=d.url),u&&(o.lcpResourceEntry=u)}}return Object.assign(i,{attribution:o})}(r);e(a)},t)},Oe=[800,1800],kt=function e(t){document.prerendering?Y(function(){return e(t)}):document.readyState!=="complete"?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},Pt=function(e,t){t=t||{};var r=v("TTFB"),a=h(e,r,Oe,t.reportAllChanges);kt(function(){var i=I();i&&(r.value=Math.max(i.responseStart-Z(),0),r.entries=[i],a(!0),A(function(){r=v("TTFB",0),(a=h(e,r,Oe,t.reportAllChanges))(!0)}))})},Ke=function(e,t){Pt(function(r){var a=function(i){var o={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(i.entries.length){var n=i.entries[0],s=n.activationStart||0,d=Math.max((n.workerStart||n.fetchStart)-s,0),u=Math.max(n.domainLookupStart-s,0),l=Math.max(n.connectStart-s,0),c=Math.max(n.connectEnd-s,0);o={waitingDuration:d,cacheDuration:u-d,dnsDuration:l-u,connectionDuration:c-l,requestDuration:i.value-c,navigationEntry:n}}return Object.assign(i,{attribution:o})}(r);e(a)},t)};function Mt(e){for(let t in e)if(e[t]!==void 0)return!0;return!1}function L(e){return Mt(e)?e:void 0}function tt(){let e=document.getElementById("main");if(!e)return;let t=new Set;try{let o=n=>t.add(n);Ye(o),qe(o),Ue(({delta:n,...s})=>{t.add({...s,delta:n*1e3})}),Xe(o),Ke(o)}catch{}let r=new Set([...performance.getEntriesByType("mark"),...performance.getEntriesByType("measure")].filter(o=>o.name.startsWith("framer-")));new PerformanceObserver(o=>{o.getEntries().forEach(n=>{n.name.startsWith("framer-")&&r.add(n)})}).observe({entryTypes:["measure","mark"]});let a=e.dataset,i={pageOptimizedAt:a.framerPageOptimizedAt?new Date(a.framerPageOptimizedAt).getTime():null,ssrReleasedAt:a.framerSsrReleasedAt?new Date(a.framerSsrReleasedAt).getTime():null,origin:document.location.origin,pathname:document.location.pathname,search:document.location.search};C(()=>zt(i),"load"),C(()=>Dt(t,r,i),"lazy")}var et=!1;function Dt(e,t,r){let a=document.getElementById("main");if(!a)return;let i=[];if(et||(i.push(xt(r,a)),et=!0),e.size>0&&(i.push(...Ft(e,r)),e.clear()),t.size>0){let o=Rt(t);o&&i.push(o),t.clear()}y(i)}function xt({pageOptimizedAt:e,ssrReleasedAt:t,origin:r,pathname:a,search:i},o){var d,u,l,c,m,g;let n=performance.getEntriesByType("navigation")[0],s=document.querySelector("[data-framer-css-ssr-minified]");return new f("published_site_performance",{hydrationDurationMs:null,pageLoadDurationMs:null,domNodes:document.getElementsByTagName("*").length,resourcesCount:performance.getEntriesByType("resource").length,headSize:document.head.innerHTML.length,framerCSSSize:(d=s==null?void 0:s.textContent)==null?void 0:d.length,modulePreloads:document.querySelectorAll(`link[rel="modulepreload"][href^="${T().cdn}"]`).length,hasPageContent:o.dataset["framer-no-content"]===void 0,timeZone:_,pageOptimizedAt:e,ssrReleasedAt:t,devicePixelRatio:window.devicePixelRatio,timeToFirstByteMs:null,navigationTiming:n?{activationStart:n.activationStart,connectEnd:n.connectEnd,connectStart:n.connectStart,criticalCHRestart:n.criticalCHRestart,decodedBodySize:n.decodedBodySize,deliveryType:n.deliveryType,domComplete:n.domComplete,domContentLoadedEventEnd:n.domContentLoadedEventEnd,domContentLoadedEventStart:n.domContentLoadedEventStart,domInteractive:n.domInteractive,domainLookupEnd:n.domainLookupEnd,domainLookupStart:n.domainLookupStart,duration:n.duration,encodedBodySize:n.encodedBodySize,fetchStart:n.fetchStart,firstInterimResponseStart:n.firstInterimResponseStart,loadEventEnd:n.loadEventEnd,loadEventStart:n.loadEventStart,nextHopProtocol:n.nextHopProtocol,redirectCount:n.redirectCount,redirectEnd:n.redirectEnd,redirectStart:n.redirectStart,requestStart:n.requestStart,responseEnd:n.responseEnd,responseStart:n.responseStart,responseStatus:n.responseStatus,secureConnectionStart:n.secureConnectionStart,serverTiming:n.serverTiming?JSON.stringify(n.serverTiming):null,startTime:n.startTime,transferSize:n.transferSize,type:n.type,unloadEventEnd:n.unloadEventEnd,unloadEventStart:n.unloadEventStart,workerStart:n.workerStart}:void 0,connection:L({downlink:(u=navigator.connection)==null?void 0:u.downlink,downlinkMax:(l=navigator.connection)==null?void 0:l.downlinkMax,rtt:(c=navigator.connection)==null?void 0:c.rtt,saveData:(m=navigator.connection)==null?void 0:m.saveData,type:(g=navigator.connection)==null?void 0:g.type}),context:{origin:r,pathname:a,search:i}})}function Ft(e,{pageOptimizedAt:t,ssrReleasedAt:r,origin:a,pathname:i,search:o}){let n=[];return e.forEach(s=>{e.delete(s);let{name:d,delta:u,id:l,attribution:c}=s,m={metric:d,label:l,value:Math.round(u),pageOptimizedAt:t,ssrReleasedAt:r,context:{origin:a,pathname:i,search:o},attributionLcp:void 0,attributionCls:void 0,attributionInp:void 0,attributionFcp:void 0,attributionTtfb:void 0};d==="LCP"?m.attributionLcp=L({element:c.element,timeToFirstByte:c.timeToFirstByte,resourceLoadDelay:c.resourceLoadDelay,resourceLoadTime:c.resourceLoadDuration,elementRenderDelay:c.elementRenderDelay,url:c.url}):d==="CLS"?m.attributionCls=L({largestShiftTarget:c.largestShiftTarget,largestShiftTime:c.largestShiftTime,largestShiftValue:c.largestShiftValue,loadState:c.loadState}):d==="INP"?m.attributionInp=L({eventTarget:c.interactionTarget,eventType:c.interactionType,eventTime:c.interactionTime?Math.round(c.interactionTime):void 0,loadState:c.loadState,inputDelay:c.inputDelay,processingDuration:c.processingDuration,presentationDelay:c.presentationDelay,nextPaintTime:c.nextPaintTime}):d==="FCP"?m.attributionFcp=L({timeToFirstByte:c.timeToFirstByte,firstByteToFCP:c.firstByteToFCP,loadState:c.loadState}):d==="TTFB"&&(m.attributionTtfb=L({waitingTime:c.waitingDuration,dnsTime:c.dnsDuration,connectionTime:c.connectionDuration,requestTime:c.requestDuration,cacheDuration:c.cacheDuration})),n.push(new f("published_site_performance_web_vitals",m))}),n}function Rt(e){let t=[];if(e.forEach(r=>{e.delete(r);let{name:a,startTime:i,duration:o,detail:n}=r,s={name:a,startTime:i,duration:o,detail:n};t.push(s)}),t.length!==0)return new f("published_site_performance_user_timings",{timings:JSON.stringify(t)})}async function zt({origin:e,pathname:t,search:r}){let a=document.getElementById("main");if(!a)return;await w();let i=1/0,o=null,n=null,s=0,d=0,u=0,l=0,c=T().cdn,m=`^${c}[^/]+/`,g=".[^.]+.mjs$",k=new RegExp(`${m}script_main${g}`),K=new RegExp(`${m}framer${g}`),S=new RegExp(`${m}motion${g}`),H=performance.getEntriesByType("resource"),Te=H.length;for(let ee=0;ee<Te;ee++){let rt=H[ee],{deliveryType:ye,initiatorType:it,transferSize:at,decodedBodySize:te,encodedBodySize:ot,name:U,startTime:Se}=rt;Se>i||!(it==="script"&&U.startsWith(c))||(++u,(ye==="cache"||ye!==void 0&&at===0)&&++l,s+=ot,d+=te,k.test(U)?i=Se:o===null&&K.test(U)?o=te:n===null&&S.test(U)&&(n=te))}await w();let P=performance.getEntriesByType("navigation")[0];y([new f("published_site_performance_load",{pageLoadDurationMs:(P==null?void 0:P.domContentLoadedEventEnd)!==void 0&&P.domContentLoadedEventStart!==void 0?Math.round(P.domContentLoadedEventEnd-P.domContentLoadedEventStart):null,resourcesCount:Te,domNodes:document.getElementsByTagName("*").length,headSize:document.head.innerHTML.length,headDomNodes:document.head.getElementsByTagName("*").length,bodySize:document.body.innerHTML.length,bodyDomNodes:document.body.getElementsByTagName("*").length,reactRootSize:a.innerHTML.length,reactRootDomNodes:a.getElementsByTagName("*").length,jsSizeDecoded:d,jsSizeEncoded:s,jsCountCached:l,jsCountTotal:u,mainScriptStartTime:Number.isFinite(i)?i:null,libraryJSSizeDecoded:o,motionJSSizeDecoded:n,context:{origin:e,pathname:t,search:r}})])}function nt(){window.__send_framer_event=(e,t)=>{let r=new f(e,t);y([r],"eager")}}var It=T(),At=async()=>{if(await w(!0),Ee(),It.trackNavigation){xe();let e=typeof document.referrer=="string";W({initialReferrer:e&&document.referrer||null})}tt(),nt(),De()};At();})();
