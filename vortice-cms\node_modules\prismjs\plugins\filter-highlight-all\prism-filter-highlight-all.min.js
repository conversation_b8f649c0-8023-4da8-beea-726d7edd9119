!function(){if("undefined"!=typeof Prism&&"undefined"!=typeof document){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var e,t=Prism.util.currentScript(),n=[],r=Prism.plugins.filterHighlightAll={add:function(e){n.push((function(t){return e({element:t,language:Prism.util.getLanguage(t)})}))},addSelector:function(e){n.push((function(t){return t.matches(e)}))},reject:{add:function(e){n.push((function(t){return!e({element:t,language:Prism.util.getLanguage(t)})}))},addSelector:function(e){n.push((function(t){return!t.matches(e)}))}},filterKnown:!!t&&t.hasAttribute("data-filter-known")};r.add((function(e){return!r.filterKnown||"object"==typeof Prism.languages[e.language]})),t&&((e=t.getAttribute("data-filter-selector"))&&r.addSelector(e),(e=t.getAttribute("data-reject-selector"))&&r.reject.addSelector(e)),Prism.hooks.add("before-all-elements-highlight",(function(e){e.elements=e.elements.filter(i)}))}function i(e){for(var t=0,r=n.length;t<r;t++)if(!n[t](e))return!1;return!0}}();